
import type {NextConfig} from 'next';
// @ts-ignore - next-pwa doesn't have proper TypeScript definitions
import withPWAInit from 'next-pwa'; // Renamed import to avoid conflict

const nextConfig: NextConfig = {
  /* config options here */
  productionBrowserSourceMaps: true, // Explicitly enable source maps
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Additional security and performance configurations
  poweredByHeader: false, // Remove X-Powered-By header
  compress: true, // Enable gzip compression

  // Webpack configuration for PDF.js and other Node.js modules
  webpack: (config, { isServer }) => {
    // Handle PDF.js worker and canvas issues
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        canvas: false,
        encoding: false,
        fs: false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        url: false,
        buffer: false,
        util: false,
        assert: false,
        events: false,
      };
    }

    // Ignore specific modules that cause issues
    config.externals = config.externals || [];
    config.externals.push({
      canvas: 'canvas',
      'utf-8-validate': 'utf-8-validate',
      'bufferutil': 'bufferutil',
    });

    // Suppress handlebars warnings from Genkit
    config.ignoreWarnings = [
      /require\.extensions is not supported by webpack/,
      /Can't resolve '@opentelemetry\/exporter-jaeger'/,
    ];

    return config;
  },

  // Security headers (additional to middleware)
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
        ],
      },
      // Specific headers for static assets
      {
        source: '/(.*)\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

const withPWA = withPWAInit({
  dest: 'public',
  register: true,
  skipWaiting: true,
  // You can disable PWA in development to avoid issues with HMR
  // disable: process.env.NODE_ENV === 'development', 
});

export default withPWA(nextConfig);
