
// src/lib/firebase.ts
import { initializeApp, getApps, type FirebaseApp } from 'firebase/app';
import { getAuth, type Auth } from 'firebase/auth';
import { getFirestore, type Firestore } from 'firebase/firestore';
import { getAnalytics, isSupported, type Analytics } from 'firebase/analytics'; // Added Analytics imports

// These should be loaded from .env (or your hosting environment variables)
// and MUST be prefixed with NEXT_PUBLIC_ to be available on the client.
const apiKey = process.env.NEXT_PUBLIC_FIREBASE_API_KEY;
const authDomain = process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN;
const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID;
const storageBucket = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET;
const messagingSenderId = process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID;
const appId = process.env.NEXT_PUBLIC_FIREBASE_APP_ID;
const measurementId = process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID;

let app: FirebaseApp;
let authInstance: Auth;
let firestoreInstance: Firestore;
let analyticsInstance: Analytics | null = null; // Added Analytics instance variable

if (!apiKey || !projectId) {
  console.error(
    '[firebase.ts] CRITICAL ERROR: NEXT_PUBLIC_FIREBASE_API_KEY or NEXT_PUBLIC_FIREBASE_PROJECT_ID is missing or undefined as read from process.env. Check your .env file, ensure variables are prefixed with NEXT_PUBLIC_, and that you have restarted your Next.js development server.'
  );
  if (!apiKey) console.error('[firebase.ts] Detail: NEXT_PUBLIC_FIREBASE_API_KEY value is missing.');
  if (!projectId) console.error('[firebase.ts] Detail: NEXT_PUBLIC_FIREBASE_PROJECT_ID value is missing.');
}

const firebaseConfig = {
  apiKey: apiKey,
  authDomain: authDomain,
  projectId: projectId,
  storageBucket: storageBucket,
  messagingSenderId: messagingSenderId,
  appId: appId,
  measurementId: measurementId, // Important for Analytics
};

if (!getApps().length) {
  if (firebaseConfig.apiKey && firebaseConfig.projectId) {
    console.log(
      `[firebase.ts] Initializing new Firebase app. Project ID: "${firebaseConfig.projectId}".`
    );
    app = initializeApp(firebaseConfig);
  } else {
    console.error(
      '[firebase.ts] Firebase app initialization SKIPPED due to missing critical configuration (API_KEY or PROJECT_ID was falsy when creating firebaseConfig). Please check values in .env and restart your dev server.'
    );
    // @ts-expect-error: Intentionally setting to null for error state
    app = null;
  }
} else {
  app = getApps()[0];
   console.log(
    `[firebase.ts] Using existing Firebase app instance. Project ID: "${app.options.projectId}".`
  );
  if (app.options.projectId !== firebaseConfig.projectId && firebaseConfig.projectId) {
     console.warn(`[firebase.ts] WARNING: Existing Firebase app project ID ("${app.options.projectId}") does not match expected project ID ("${firebaseConfig.projectId}") from .env. This can cause issues.`);
  }
}

if (app) {
  authInstance = getAuth(app);
  firestoreInstance = getFirestore(app);

  // Initialize Analytics only on the client side and if supported
  if (typeof window !== 'undefined') {
    isSupported().then((supported) => {
      if (supported && firebaseConfig.measurementId) {
        analyticsInstance = getAnalytics(app);
        console.log('[firebase.ts] Firebase Analytics initialized.');
      } else if (!firebaseConfig.measurementId) {
        console.warn('[firebase.ts] Firebase Analytics NOT initialized: NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID is missing.');
      } else {
        console.warn('[firebase.ts] Firebase Analytics NOT initialized: Browser does not support it or other configuration issue.');
      }
    });
  }

} else {
  console.error('[firebase.ts] Firebase app object is null after initialization attempt. Auth, Firestore, and Analytics will not be available. Review previous logs for configuration errors.');
  // @ts-expect-error: Intentionally setting to null for error state
  authInstance = null;
  // @ts-expect-error: Intentionally setting to null for error state
  firestoreInstance = null;
}

export { app, authInstance as auth, firestoreInstance as firestore, analyticsInstance as analytics };
