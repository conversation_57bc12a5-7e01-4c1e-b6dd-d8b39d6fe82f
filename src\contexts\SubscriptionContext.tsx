
// src/contexts/SubscriptionContext.tsx
'use client';
import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth } from './AuthContext';
import type { UserSubscriptionData } from '@/types';
import { ensureUserSubscription } from '@/services/userService'; // Corrected: was getUserSubscription

interface SubscriptionContextType {
  subscription: UserSubscriptionData | null;
  loadingSubscription: boolean;
  refreshSubscription: () => Promise<void>;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider = ({ children }: { children: ReactNode }) => {
  const { user, loading: authLoading } = useAuth();
  const [subscription, setSubscription] = useState<UserSubscriptionData | null>(null);
  const [loadingSubscription, setLoadingSubscription] = useState(true);

  const refreshSubscription = useCallback(async () => {
    if (user && user.uid) {
      setLoadingSubscription(true);
      try {
        // ensureUserSubscription will create/update the subscription doc in Firestore
        // and return the latest state, including any daily resets.
        const subData = await ensureUserSubscription(user.uid);
        setSubscription(subData);
      } catch (error) {
        console.error("[SubscriptionContext] Error fetching or ensuring user subscription:", error);
        setSubscription(null);
      } finally {
        setLoadingSubscription(false);
      }
    } else {
      setSubscription(null);
      setLoadingSubscription(false);
    }
  }, [user]); // Depends on user object

  useEffect(() => {
    // Trigger refresh when auth loading is done OR when user object changes
    if (!authLoading && user) {
      refreshSubscription();
    } else if (!authLoading && !user) {
      // User logged out, clear subscription
      setSubscription(null);
      setLoadingSubscription(false);
    }
  }, [user, authLoading, refreshSubscription]);

  return (
    <SubscriptionContext.Provider value={{ subscription, loadingSubscription, refreshSubscription }}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = (): SubscriptionContextType => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};
