'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Props {
  children: ReactNode;
  onRetry?: () => void;
  onSkipAI?: () => void;
  operationType?: 'suggestion' | 'analysis' | 'generation';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorId: string;
}

/**
 * Specialized Error Boundary for AI Operations
 * 
 * Handles errors specifically related to AI-powered features like keyword suggestions,
 * content analysis, and metadata generation. Provides graceful degradation options.
 */
export class AIOperationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `ai_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[AIOperationErrorBoundary] AI operation error:', error);
    console.error('[AIOperationErrorBoundary] Error info:', errorInfo);

    // Log AI-specific errors
    this.logAIError(error, errorInfo);
  }

  private logAIError = (error: Error, errorInfo: ErrorInfo) => {
    const errorData = {
      type: 'ai_operation_error',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      context: 'ai_operations',
      operationType: this.props.operationType || 'unknown',
    };

    console.error('[AIOperationErrorBoundary] AI error logged:', errorData);
    
    // TODO: Send to error tracking service with AI operation context
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorId: '',
    });

    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  private handleSkipAI = () => {
    this.setState({
      hasError: false,
      error: null,
      errorId: '',
    });

    if (this.props.onSkipAI) {
      this.props.onSkipAI();
    }
  };

  private getErrorMessage = (error: Error): { title: string; description: string; isQuotaError: boolean } => {
    const message = error.message.toLowerCase();
    
    if (message.includes('429') || message.includes('quota') || message.includes('rate limit')) {
      return {
        title: 'AI Service Limit Reached',
        description: 'You\'ve reached the AI service usage limit. Please try again later or upgrade your plan for more AI operations.',
        isQuotaError: true,
      };
    }
    
    if (message.includes('401') || message.includes('unauthorized') || message.includes('api key')) {
      return {
        title: 'AI Service Authentication Error',
        description: 'There\'s an issue with the AI service authentication. Please try again or contact support if the problem persists.',
        isQuotaError: false,
      };
    }
    
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return {
        title: 'AI Service Connection Error',
        description: 'Unable to connect to the AI service. Please check your internet connection and try again.',
        isQuotaError: false,
      };
    }
    
    if (message.includes('content') && message.includes('too large')) {
      return {
        title: 'Content Too Large for AI Processing',
        description: 'The document content is too large for AI processing. Try with a smaller document or contact support.',
        isQuotaError: false,
      };
    }

    return {
      title: 'AI Service Error',
      description: 'The AI service encountered an unexpected error. You can continue using the app without AI features or try again.',
      isQuotaError: false,
    };
  };

  private getOperationName = (): string => {
    switch (this.props.operationType) {
      case 'suggestion':
        return 'AI Suggestions';
      case 'analysis':
        return 'Content Analysis';
      case 'generation':
        return 'Content Generation';
      default:
        return 'AI Operation';
    }
  };

  render() {
    if (this.state.hasError) {
      const { error, errorId } = this.state;
      const { title, description, isQuotaError } = error ? this.getErrorMessage(error) : {
        title: 'AI Service Error',
        description: 'An unknown error occurred with the AI service.',
        isQuotaError: false,
      };

      const operationName = this.getOperationName();

      return (
        <div className="flex min-h-[250px] items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-10 w-10 text-destructive">
                {isQuotaError ? <Zap className="h-full w-full" /> : <Bot className="h-full w-full" />}
              </div>
              <CardTitle className="text-lg">{title}</CardTitle>
              <CardDescription>
                {operationName} is temporarily unavailable.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {description}
                </AlertDescription>
              </Alert>

              <Alert>
                <AlertDescription>
                  Error ID: <code className="text-sm font-mono">{errorId}</code>
                </AlertDescription>
              </Alert>

              {isQuotaError && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">What you can do:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Wait and try again later</li>
                    <li>• Upgrade to a higher plan for more AI operations</li>
                    <li>• Continue editing metadata manually</li>
                  </ul>
                </div>
              )}

              <div className="flex flex-col gap-2 sm:flex-row">
                <Button onClick={this.handleRetry} className="flex-1">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
                <Button onClick={this.handleSkipAI} variant="outline" className="flex-1">
                  Continue Without AI
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AIOperationErrorBoundary;
