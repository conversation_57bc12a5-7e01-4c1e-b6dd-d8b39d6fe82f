
'use client';

import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from '@/contexts/AuthContext';
import { SubscriptionProvider } from '@/contexts/SubscriptionContext';
import Navbar from '@/components/Navbar';
import { Inter, Outfit } from 'next/font/google';
import { analytics } from '@/lib/firebase';
import { useEffect } from 'react';

import { ErrorBoundary } from '@/components/ErrorBoundary';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  weight: ['400', '500', '600', '700'],
});

const outfit = Outfit({
  subsets: ['latin'],
  variable: '--font-outfit',
  display: 'swap', // Add display swap
});

const siteBaseUrl = 'https://www.metapdf.app'; 

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  useEffect(() => {
    if (typeof window !== 'undefined' && analytics) {
      // Analytics instance available
    }
  }, []);

  const pageTitle = "MetaPDF | Edit, Optimize & Manage PDF Metadata Easily";
  const pageDescription = "MetaPDF: The ultimate tool to effortlessly edit PDF metadata, optimize for SEO with AI suggestions, manage multiple files, and apply templates. Take control of your PDFs.";
  const ogImageUrl = `${siteBaseUrl}/og-image.png`; 
  const twitterImageUrl = `${siteBaseUrl}/twitter-image.png`;


  return (
    <html lang="en" className={`${inter.variable} ${outfit.variable}`}>
      <head>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        
        {/* Resource Hints for Third-Party Scripts */}
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://www.google-analytics.com" />
        <link rel="preconnect" href="https://firebaseinstallations.googleapis.com" />
        <link rel="preconnect" href="https://apis.google.com" />
        <link rel="preconnect" href="https://www.googleapis.com" />
        <link rel="preconnect" href="https://metapdf-ad495.firebaseapp.com" />
        <link rel="preconnect" href="https://firebase.googleapis.com" />


        {/* Favicons and Manifest */}
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-icon.png" /> 
        <meta name="theme-color" content="#3F51B5" />
        <meta name="msapplication-TileColor" content="#3F51B5" />
        <meta name="msapplication-config" content="/browserconfig.xml" />


        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={siteBaseUrl} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:image" content={ogImageUrl} data-ai-hint="website social" />
        <meta property="og:site_name" content="MetaPDF" />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:url" content={siteBaseUrl} />
        <meta name="twitter:title" content={pageTitle} />
        <meta name="twitter:description" content={pageDescription} />
        <meta name="twitter:image" content={twitterImageUrl} data-ai-hint="website social media" />
      </head>
      <body className="font-body antialiased">
        <ErrorBoundary level="critical">
          <AuthProvider>
            <SubscriptionProvider>
              <ErrorBoundary level="page">
                <Navbar />
                <main>{children}</main>
                <Toaster />
              </ErrorBoundary>
            </SubscriptionProvider>
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
