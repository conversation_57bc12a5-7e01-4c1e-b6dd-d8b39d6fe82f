// Global type declarations for libraries without TypeScript definitions

declare module 'caseless' {
  interface Caseless {
    (name: string): string;
    set(name: string, value: string, clobber?: boolean): string | undefined;
    get(name: string): string | undefined;
    has(name: string): string | undefined;
    del(name: string): boolean;
    clear(): void;
    swap(other: Caseless): void;
  }

  function caseless(dict: Record<string, string | string[]>): Caseless;
  export = caseless;
}

declare module 'estree' {
  export interface Node {
    type: string;
    loc?: SourceLocation | null;
    range?: [number, number];
  }

  export interface SourceLocation {
    source?: string | null;
    start: Position;
    end: Position;
  }

  export interface Position {
    line: number;
    column: number;
  }

  export interface Program extends Node {
    type: 'Program';
    body: Statement[];
    sourceType: 'script' | 'module';
  }

  export interface Statement extends Node {
    type: string;
  }
  export interface Expression extends Node {
    type: string;
  }
  export interface Pattern extends Node {
    type: string;
  }
  export interface Declaration extends Statement {
    type: string;
  }
}

declare module 'request' {
  import { IncomingMessage } from 'http';
  import { Stream } from 'stream';

  interface RequestResponse extends IncomingMessage {
    statusCode: number;
    statusMessage: string;
    request: Request;
    body: string | Buffer | Record<string, unknown>;
    caseless: (name: string) => string | undefined;
    toJSON(): Record<string, unknown>;
  }

  interface RequestOptions {
    url?: string;
    uri?: string;
    method?: string;
    headers?: { [key: string]: string };
    body?: string | Buffer | Record<string, unknown>;
    json?: boolean;
    form?: Record<string, unknown>;
    formData?: Record<string, unknown>;
    qs?: Record<string, unknown>;
    timeout?: number;
    followRedirect?: boolean;
    maxRedirects?: number;
    encoding?: string | null;
    gzip?: boolean;
    jar?: CookieJar;
    agent?: unknown;
    auth?: {
      user?: string;
      username?: string;
      pass?: string;
      password?: string;
      sendImmediately?: boolean;
      bearer?: string;
    };
  }

  interface CookieJar {
    getCookieString(url: string): string;
    setCookie(cookie: string, url: string): void;
  }

  interface Request extends Stream {
    abort(): void;
    end(): void;
    pause(): void;
    resume(): void;
    destroy(): void;
  }

  interface RequestAPI {
    (options: RequestOptions, callback?: (error: Error | null, response: RequestResponse, body: string | Buffer | Record<string, unknown>) => void): Request;
    (url: string, callback?: (error: Error | null, response: RequestResponse, body: string | Buffer | Record<string, unknown>) => void): Request;
    (url: string, options: RequestOptions, callback?: (error: Error | null, response: RequestResponse, body: string | Buffer | Record<string, unknown>) => void): Request;

    get: RequestAPI;
    post: RequestAPI;
    put: RequestAPI;
    patch: RequestAPI;
    del: RequestAPI;
    delete: RequestAPI;
    head: RequestAPI;
    options: RequestAPI;
  }

  const request: RequestAPI;
  export = request;
}

declare module 'tough-cookie' {
  interface CookieProperties {
    key?: string;
    value?: string;
    expires?: Date;
    maxAge?: number;
    domain?: string;
    path?: string;
    secure?: boolean;
    httpOnly?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
  }

  export class Cookie {
    constructor(properties?: CookieProperties);
    key: string;
    value: string;
    expires?: Date;
    maxAge?: number;
    domain?: string;
    path?: string;
    secure?: boolean;
    httpOnly?: boolean;
    sameSite?: string;

    toString(): string;
    cookieString(): string;
    setExpires(exp: Date): void;
    setMaxAge(age: number): void;
    expiryTime(now?: number): number;
    expiryDate(now?: number): Date;
    TTL(now?: number): number;
    isPersistent(): boolean;
    canonicalizedDomain(): string;
    cdomain(): string;
    inspect(): string;
    toJSON(): Record<string, unknown>;
    clone(): Cookie;
    validate(): boolean;
  }

  interface CookieJarOptions {
    rejectPublicSuffixes?: boolean;
    looseMode?: boolean;
    prefixSecurity?: 'strict' | 'silent' | 'disabled';
    allowSpecialUseDomain?: boolean;
  }

  interface CookieStore {
    findCookie(domain: string, path: string, key: string, callback: (err: Error | null, cookie?: Cookie) => void): void;
    findCookies(domain: string, path: string, allowSpecialUseDomain: boolean, callback: (err: Error | null, cookies?: Cookie[]) => void): void;
    putCookie(cookie: Cookie, callback: (err: Error | null) => void): void;
    updateCookie(oldCookie: Cookie, newCookie: Cookie, callback: (err: Error | null) => void): void;
    removeCookie(domain: string, path: string, key: string, callback: (err: Error | null) => void): void;
    removeCookies(domain: string, path: string, callback: (err: Error | null) => void): void;
    removeAllCookies(callback: (err: Error | null) => void): void;
    getAllCookies(callback: (err: Error | null, cookies?: Cookie[]) => void): void;
  }

  export class CookieJar {
    constructor(store?: CookieStore, options?: CookieJarOptions);
    setCookie(cookieOrString: Cookie | string, currentUrl: string, options?: CookieJarOptions, callback?: (err: Error | null, cookie?: Cookie) => void): void;
    setCookieSync(cookieOrString: Cookie | string, currentUrl: string, options?: CookieJarOptions): Cookie;
    getCookies(currentUrl: string, options?: CookieJarOptions, callback?: (err: Error | null, cookies?: Cookie[]) => void): void;
    getCookiesSync(currentUrl: string, options?: CookieJarOptions): Cookie[];
    getCookieString(currentUrl: string, options?: CookieJarOptions, callback?: (err: Error | null, cookies?: string) => void): void;
    getCookieStringSync(currentUrl: string, options?: CookieJarOptions): string;
    getSetCookieStrings(currentUrl: string, options?: CookieJarOptions, callback?: (err: Error | null, cookies?: string[]) => void): void;
    getSetCookieStringsSync(currentUrl: string, options?: CookieJarOptions): string[];
    serialize(callback: (err: Error | null, serializedJar?: Record<string, unknown>) => void): void;
    serializeSync(): Record<string, unknown>;
    toJSON(): Record<string, unknown>;
    clone(callback?: (err: Error | null, jar?: CookieJar) => void): void;
    cloneSync(): CookieJar;
  }

  interface ParseOptions {
    loose?: boolean;
  }

  export function parse(cookieString: string, options?: ParseOptions): Cookie | undefined;
  export function fromJSON(string: string): Cookie;
}

declare module 'trusted-types' {
  export interface TrustedHTML {
    toString(): string;
  }

  export interface TrustedScript {
    toString(): string;
  }

  export interface TrustedScriptURL {
    toString(): string;
  }

  export interface TrustedTypePolicyOptions {
    createHTML?(input: string, ...args: unknown[]): TrustedHTML;
    createScript?(input: string, ...args: unknown[]): TrustedScript;
    createScriptURL?(input: string, ...args: unknown[]): TrustedScriptURL;
  }

  export interface TrustedTypePolicy {
    name: string;
    createHTML?(input: string, ...args: unknown[]): TrustedHTML;
    createScript?(input: string, ...args: unknown[]): TrustedScript;
    createScriptURL?(input: string, ...args: unknown[]): TrustedScriptURL;
  }

  export interface TrustedTypePolicyFactory {
    createPolicy(policyName: string, policyOptions?: TrustedTypePolicyOptions): TrustedTypePolicy;
    isHTML(value: unknown): value is TrustedHTML;
    isScript(value: unknown): value is TrustedScript;
    isScriptURL(value: unknown): value is TrustedScriptURL;
    emptyHTML: TrustedHTML;
    emptyScript: TrustedScript;
    defaultPolicy?: TrustedTypePolicy;
    getAttributeType(tagName: string, attribute: string, elementNs?: string, attrNs?: string): string | null;
    getPropertyType(tagName: string, property: string, elementNs?: string): string | null;
  }

  export const trustedTypes: TrustedTypePolicyFactory;
}

// Extend the global Window interface for browser APIs
declare global {
  interface Window {
    trustedTypes?: import('trusted-types').TrustedTypePolicyFactory;
  }
}

export {};
