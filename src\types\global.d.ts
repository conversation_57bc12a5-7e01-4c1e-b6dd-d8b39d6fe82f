// Global type declarations for libraries without TypeScript definitions

declare module 'caseless' {
  interface Caseless {
    (name: string): string;
    set(name: string, value: string, clobber?: boolean): string | undefined;
    get(name: string): string | undefined;
    has(name: string): string | undefined;
    del(name: string): boolean;
    clear(): void;
    swap(other: any): void;
  }

  function caseless(dict: any): Caseless;
  export = caseless;
}

declare module 'estree' {
  export interface Node {
    type: string;
    loc?: SourceLocation | null;
    range?: [number, number];
  }

  export interface SourceLocation {
    source?: string | null;
    start: Position;
    end: Position;
  }

  export interface Position {
    line: number;
    column: number;
  }

  export interface Program extends Node {
    type: 'Program';
    body: Statement[];
    sourceType: 'script' | 'module';
  }

  export interface Statement extends Node {}
  export interface Expression extends Node {}
  export interface Pattern extends Node {}
  export interface Declaration extends Statement {}
}

declare module 'request' {
  import { IncomingMessage } from 'http';
  import { Stream } from 'stream';

  interface RequestResponse extends IncomingMessage {
    statusCode: number;
    statusMessage: string;
    request: any;
    body: any;
    caseless: any;
    toJSON(): any;
  }

  interface RequestOptions {
    url?: string;
    uri?: string;
    method?: string;
    headers?: { [key: string]: string };
    body?: any;
    json?: boolean;
    form?: any;
    formData?: any;
    qs?: any;
    timeout?: number;
    followRedirect?: boolean;
    maxRedirects?: number;
    encoding?: string | null;
    gzip?: boolean;
    jar?: any;
    agent?: any;
    auth?: {
      user?: string;
      username?: string;
      pass?: string;
      password?: string;
      sendImmediately?: boolean;
      bearer?: string;
    };
  }

  interface Request extends Stream {
    abort(): void;
    end(): void;
    pause(): void;
    resume(): void;
    destroy(): void;
  }

  interface RequestAPI {
    (options: RequestOptions, callback?: (error: any, response: RequestResponse, body: any) => void): Request;
    (url: string, callback?: (error: any, response: RequestResponse, body: any) => void): Request;
    (url: string, options: RequestOptions, callback?: (error: any, response: RequestResponse, body: any) => void): Request;
    
    get: RequestAPI;
    post: RequestAPI;
    put: RequestAPI;
    patch: RequestAPI;
    del: RequestAPI;
    delete: RequestAPI;
    head: RequestAPI;
    options: RequestAPI;
  }

  const request: RequestAPI;
  export = request;
}

declare module 'tough-cookie' {
  export class Cookie {
    constructor(properties?: any);
    key: string;
    value: string;
    expires?: Date;
    maxAge?: number;
    domain?: string;
    path?: string;
    secure?: boolean;
    httpOnly?: boolean;
    sameSite?: string;
    
    toString(): string;
    cookieString(): string;
    setExpires(exp: Date): void;
    setMaxAge(age: number): void;
    expiryTime(now?: number): number;
    expiryDate(now?: number): Date;
    TTL(now?: number): number;
    isPersistent(): boolean;
    canonicalizedDomain(): string;
    cdomain(): string;
    inspect(): string;
    toJSON(): any;
    clone(): Cookie;
    validate(): boolean;
  }

  export class CookieJar {
    constructor(store?: any, options?: any);
    setCookie(cookieOrString: Cookie | string, currentUrl: string, options?: any, callback?: (err: Error | null, cookie?: Cookie) => void): void;
    setCookieSync(cookieOrString: Cookie | string, currentUrl: string, options?: any): Cookie;
    getCookies(currentUrl: string, options?: any, callback?: (err: Error | null, cookies?: Cookie[]) => void): void;
    getCookiesSync(currentUrl: string, options?: any): Cookie[];
    getCookieString(currentUrl: string, options?: any, callback?: (err: Error | null, cookies?: string) => void): void;
    getCookieStringSync(currentUrl: string, options?: any): string;
    getSetCookieStrings(currentUrl: string, options?: any, callback?: (err: Error | null, cookies?: string[]) => void): void;
    getSetCookieStringsSync(currentUrl: string, options?: any): string[];
    serialize(callback: (err: Error | null, serializedJar?: any) => void): void;
    serializeSync(): any;
    toJSON(): any;
    clone(callback?: (err: Error | null, jar?: CookieJar) => void): void;
    cloneSync(): CookieJar;
  }

  export function parse(cookieString: string, options?: any): Cookie | undefined;
  export function fromJSON(string: string): Cookie;
}

declare module 'trusted-types' {
  export interface TrustedHTML {
    toString(): string;
  }

  export interface TrustedScript {
    toString(): string;
  }

  export interface TrustedScriptURL {
    toString(): string;
  }

  export interface TrustedTypePolicy {
    name: string;
    createHTML?(input: string, ...arguments: any[]): TrustedHTML;
    createScript?(input: string, ...arguments: any[]): TrustedScript;
    createScriptURL?(input: string, ...arguments: any[]): TrustedScriptURL;
  }

  export interface TrustedTypePolicyFactory {
    createPolicy(policyName: string, policyOptions?: any): TrustedTypePolicy;
    isHTML(value: any): value is TrustedHTML;
    isScript(value: any): value is TrustedScript;
    isScriptURL(value: any): value is TrustedScriptURL;
    emptyHTML: TrustedHTML;
    emptyScript: TrustedScript;
    defaultPolicy?: TrustedTypePolicy;
    getAttributeType(tagName: string, attribute: string, elementNs?: string, attrNs?: string): string | null;
    getPropertyType(tagName: string, property: string, elementNs?: string): string | null;
  }

  export const trustedTypes: TrustedTypePolicyFactory;
}

// Extend the global Window interface for browser APIs
declare global {
  interface Window {
    trustedTypes?: import('trusted-types').TrustedTypePolicyFactory;
  }
}

export {};
