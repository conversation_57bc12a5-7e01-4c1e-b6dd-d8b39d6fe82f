# Error Handling System

MetaPDF implements a comprehensive error handling system with multiple layers of error boundaries and graceful degradation strategies.

## Overview

The error handling system consists of:

1. **Global Error Boundaries** - Catch and handle application-wide errors
2. **Specialized Error Boundaries** - Handle specific operation types
3. **Error Pages** - Next.js error pages for routing and server errors
4. **Error Logging** - Comprehensive error tracking and reporting
5. **User-Friendly Fallbacks** - Graceful degradation with recovery options

## Error Boundary Components

### 1. Global Error Boundary (`ErrorBoundary`)

**Location**: `src/components/ErrorBoundary.tsx`

The main error boundary component that catches JavaScript errors anywhere in the component tree.

```typescript
import { ErrorBoundary } from '@/components/ErrorBoundary';

// Usage
<ErrorBoundary level="critical" showDetails={false}>
  <YourComponent />
</ErrorBoundary>
```

**Props**:
- `level`: `'page' | 'component' | 'critical'` - Error severity level
- `showDetails`: `boolean` - Show technical details (default: development only)
- `fallback`: `ReactNode` - Custom fallback UI
- `onError`: `(error, errorInfo) => void` - Custom error handler

**Features**:
- Automatic error ID generation
- Different UI based on error level
- Error reporting to external services
- Recovery options (retry, reload, go home)
- Development vs production error display

### 2. File Operation Error Boundary

**Location**: `src/components/FileOperationErrorBoundary.tsx`

Specialized for file upload, processing, and metadata operations.

```typescript
import FileOperationErrorBoundary from '@/components/FileOperationErrorBoundary';

<FileOperationErrorBoundary 
  onRetry={handleRetry}
  onClearFiles={handleClearFiles}
>
  <FileUploadComponent />
</FileOperationErrorBoundary>
```

**Features**:
- File-specific error messages
- Troubleshooting tips
- Recovery options (retry, upload new file)
- Context-aware error handling

### 3. AI Operation Error Boundary

**Location**: `src/components/AIOperationErrorBoundary.tsx`

Handles AI service errors with graceful degradation.

```typescript
import AIOperationErrorBoundary from '@/components/AIOperationErrorBoundary';

<AIOperationErrorBoundary 
  operationType="suggestion"
  onRetry={handleRetry}
  onSkipAI={handleSkipAI}
>
  <AIComponent />
</AIOperationErrorBoundary>
```

**Features**:
- AI service quota handling
- Graceful degradation options
- Operation-specific error messages
- Continue without AI functionality

## Error Pages

### Global Error Page (`error.tsx`)

**Location**: `src/app/error.tsx`

Next.js error page for handling application errors.

**Features**:
- Automatic error logging
- User-friendly error messages
- Recovery options
- Error reporting functionality
- Development vs production display

### 404 Not Found Page (`not-found.tsx`)

**Location**: `src/app/not-found.tsx`

Custom 404 page with helpful navigation.

**Features**:
- Popular pages links
- Search suggestions
- Navigation options
- Support contact information

## Error Logging and Reporting

### Error Data Structure

```typescript
interface ErrorData {
  type: string;
  message: string;
  stack?: string;
  componentStack?: string;
  errorId: string;
  timestamp: string;
  userAgent: string;
  url: string;
  level: 'page' | 'component' | 'critical';
  context?: string;
}
```

### Logging Implementation

```typescript
private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
  const errorData = {
    message: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    errorId: this.state.errorId,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown',
    level: this.props.level || 'component',
  };

  // Send to error tracking service
  // Example: Sentry.captureException(error, { extra: errorData });
};
```

## Error Types and Handling

### 1. File Processing Errors

**Common Errors**:
- File size too large
- Invalid PDF format
- Corrupted files
- Memory issues
- Network errors

**Handling Strategy**:
- Specific error messages
- File validation tips
- Alternative upload options
- Clear recovery paths

### 2. AI Service Errors

**Common Errors**:
- Rate limiting (429)
- Authentication errors (401)
- Network timeouts
- Content too large
- Service unavailable

**Handling Strategy**:
- Quota-aware messaging
- Graceful degradation
- Continue without AI option
- Retry mechanisms

### 3. Authentication Errors

**Common Errors**:
- Invalid credentials
- Session expired
- Network issues
- Service unavailable

**Handling Strategy**:
- Clear error messages
- Re-authentication prompts
- Automatic retry
- Fallback options

### 4. Network Errors

**Common Errors**:
- Connection timeout
- Server unavailable
- DNS resolution
- CORS issues

**Handling Strategy**:
- Connection status detection
- Retry mechanisms
- Offline mode indicators
- Cache utilization

## Best Practices

### 1. Error Boundary Placement

```typescript
// Application level - Critical errors
<ErrorBoundary level="critical">
  <App />
</ErrorBoundary>

// Page level - Page-specific errors
<ErrorBoundary level="page">
  <PageContent />
</ErrorBoundary>

// Component level - Feature-specific errors
<ErrorBoundary level="component">
  <FeatureComponent />
</ErrorBoundary>
```

### 2. Error Message Guidelines

- **Be specific**: Explain what went wrong
- **Be helpful**: Provide actionable solutions
- **Be reassuring**: Maintain user confidence
- **Be concise**: Avoid technical jargon

### 3. Recovery Options

Always provide multiple recovery options:
- **Retry**: Try the operation again
- **Alternative**: Provide different approach
- **Skip**: Continue without the feature
- **Reset**: Start over with clean state

### 4. Error Prevention

- Input validation
- Type checking
- Defensive programming
- Graceful degradation
- Progressive enhancement

## Testing Error Boundaries

### Manual Testing

```typescript
// Test component that throws errors
const ErrorTestComponent = ({ shouldError }: { shouldError: boolean }) => {
  if (shouldError) {
    throw new Error('Test error for boundary testing');
  }
  return <div>Normal component</div>;
};

// Usage in development
<ErrorBoundary>
  <ErrorTestComponent shouldError={true} />
</ErrorBoundary>
```

### Automated Testing

```typescript
// Jest test example
import { render } from '@testing-library/react';
import { ErrorBoundary } from '@/components/ErrorBoundary';

test('ErrorBoundary catches and displays error', () => {
  const ThrowError = () => {
    throw new Error('Test error');
  };

  const { getByText } = render(
    <ErrorBoundary>
      <ThrowError />
    </ErrorBoundary>
  );

  expect(getByText(/something went wrong/i)).toBeInTheDocument();
});
```

## Integration with External Services

### Sentry Integration (Example)

```typescript
import * as Sentry from '@sentry/nextjs';

// In error boundary
private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
  Sentry.captureException(error, {
    extra: {
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
    },
    tags: {
      section: 'error_boundary',
      level: this.props.level,
    },
  });
};
```

### Custom Analytics

```typescript
// Track error events
const trackError = (error: Error, context: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'exception', {
      description: error.message,
      fatal: false,
      custom_map: {
        context,
        error_id: generateErrorId(),
      },
    });
  }
};
```

## Monitoring and Alerts

### Error Rate Monitoring

- Track error frequency
- Monitor error types
- Alert on error spikes
- Performance impact analysis

### User Impact Assessment

- Affected user count
- Feature availability
- Recovery success rate
- User satisfaction metrics

---

This error handling system ensures that MetaPDF provides a robust, user-friendly experience even when things go wrong, with comprehensive logging and recovery options.
