
// src/config/plans.ts
import type { SubscriptionPlan, AddonPack } from '@/types';

export const PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Basic access with limited AI suggestions. Ad-supported.',
    priceMonthly: 0,
    features: [
      'Process up to 3 PDF files at a time',
      '5 AI Keyword Suggestions per day',
      '5 AI Subject Suggestions per day',
      'Basic Metadata Editing (Bulk Edit, Rename, Save, Clear)',
      'Community Support',
      'Contains advertisements',
    ],
    limits: {
      maxFiles: 3,
      keywordSuggestionsPerDay: 5,
      subjectSuggestionsPerDay: 5,
    },
    hasAds: true,
    // No Lemon Squeezy checkout for free plan directly
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'More files, significantly more AI power, ad-free, and template saving.',
    priceMonthly: 5,
    features: [
      'Process up to 25 PDF files at a time',
      '100 AI Keyword Suggestions per day',
      '100 AI Subject Suggestions per day',
      'Advanced Metadata Editing (incl. Apply Template & Extract All)',
      'Save and Apply Metadata Templates',
      'Extract All Metadata to CSV',
      'Ad-free experience',
      'Priority Support',
    ],
    limits: {
      maxFiles: 25,
      keywordSuggestionsPerDay: 100,
      subjectSuggestionsPerDay: 100,
    },
    stripePriceId: 'price_example_pro_monthly_gbp', // Keep for reference or remove if solely LS
    lemonSqueezyCheckoutUrl: 'https://metapdf.lemonsqueezy.com/buy/9687673b-f939-4008-aef8-6a884fafe7f3', 
    lemonSqueezyVariantId: '839838', 
    hasAds: false,
  },
];

export const ADDON_PACKS: AddonPack[] = [
  {
    id: 'kw_pack_20',
    name: '20 Keyword Suggestions Pack',
    type: 'keywordSuggestions',
    quantity: 20,
    price: 1.00,
    description: 'Get 20 extra AI keyword suggestions.',
    stripePriceId: 'price_addon_kw_20_gbp', // Keep for reference or remove
    lemonSqueezyCheckoutUrl: 'https://metapdf.lemonsqueezy.com/buy/b6af6fd5-0b7c-49a7-a910-0d39872884e1', 
    lemonSqueezyVariantId: '839842', 
  },
  {
    id: 'kw_pack_50',
    name: '50 Keyword Suggestions Pack',
    type: 'keywordSuggestions',
    quantity: 50,
    price: 2.00,
    description: 'Get 50 extra AI keyword suggestions.',
    stripePriceId: 'price_addon_kw_50_gbp', // Keep for reference or remove
    lemonSqueezyCheckoutUrl: 'https://metapdf.lemonsqueezy.com/buy/a2795e1c-786f-48b6-8876-6653327ecaf1', 
    lemonSqueezyVariantId: '839843', 
  },
  {
    id: 'subj_pack_20',
    name: '20 Subject Suggestions Pack',
    type: 'subjectSuggestions',
    quantity: 20,
    price: 1.00,
    description: 'Get 20 extra AI subject/description suggestions.',
    stripePriceId: 'price_addon_subj_20_gbp', // Keep for reference or remove
    lemonSqueezyCheckoutUrl: 'https://metapdf.lemonsqueezy.com/buy/297e7ffa-18d9-4ce5-8b20-d2fcead25c47', 
    lemonSqueezyVariantId: '839844', 
  },
  {
    id: 'subj_pack_50',
    name: '50 Subject Suggestions Pack',
    type: 'subjectSuggestions',
    quantity: 50,
    price: 2.00,
    description: 'Get 50 extra AI subject/description suggestions.',
    stripePriceId: 'price_addon_subj_50_gbp', // Keep for reference or remove
    lemonSqueezyCheckoutUrl: 'https://metapdf.lemonsqueezy.com/buy/e46433c5-e48a-4ff3-a51d-b312d0242255', 
    lemonSqueezyVariantId: '839845', 
  },
];

export const DEFAULT_PLAN_ID = 'free';

// Assuming your Lemon Squeezy store subdomain is 'metapdf'
export const LEMON_SQUEEZY_CUSTOMER_PORTAL_URL = 'https://metapdf.lemonsqueezy.com/account';

