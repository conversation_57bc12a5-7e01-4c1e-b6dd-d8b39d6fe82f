
"use client";

import React, { useCallback, useMemo } from 'react';
import type { PdfFile, Metadata } from '@/types';
import { suggestKeywords } from '@/ai/flows/suggest-keywords';
import { suggestSubject } from '@/ai/flows/suggest-subject-flow';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import FileMetadataCard from './FileMetadataCard';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Label } from '@/components/ui/label';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { checkAndIncrementUsage, type AiUsageFeature } from '@/services/subscriptionService';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface MetadataDisplayTableProps {
  files: PdfFile[];
  onUpdateFile: (fileId: string, updatedMetadata: Partial<Metadata>, updatedFields?: Partial<Pick<PdfFile, 'isLoadingKeywords' | 'suggestedKeywords' | 'isLoadingSubject'>>) => void;
  onSelectFile: (fileId: string,isSelected: boolean) => void;
  selectedFileIds: Set<string>;
  isSeoView: boolean;
  openAccordionItems: string[];
  onOpenAccordionItemsChange: (items: string[]) => void;
}

const MAX_AI_INPUT_CHARS = 7000; // Reduced from 10000 to 7000

const MetadataDisplayTable: React.FC<MetadataDisplayTableProps> = ({
  files,
  onUpdateFile,
  onSelectFile,
  selectedFileIds,
  isSeoView,
  openAccordionItems,
  onOpenAccordionItemsChange,
}) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { subscription, loadingSubscription, refreshSubscription } = useSubscription();

  const handleGenericAISuggestion = useCallback(async (
    file: PdfFile,
    usageType: AiUsageFeature,
    aiOperation: (truncatedContent: string) => Promise<any>, // eslint-disable-line @typescript-eslint/no-explicit-any
    updateLoadingField: 'isLoadingKeywords' | 'isLoadingSubject',
    successCallback: (result: any, usageSource: 'daily' | 'addon' | undefined) => void // eslint-disable-line @typescript-eslint/no-explicit-any
  ) => {
    if (!user || !user.uid) {
      toast({ variant: "destructive", title: "Authentication Error", description: "Cannot verify user for AI suggestions." });
      onUpdateFile(file.id, {}, { [updateLoadingField]: false });
      return;
    }
    if (loadingSubscription) {
      toast({ title: "Please wait", description: "Loading subscription details..." });
      onUpdateFile(file.id, {}, { [updateLoadingField]: false });
      return;
    }
    if (!subscription) {
      toast({ variant: "destructive", title: "Subscription Error", description: "Could not load subscription. Please try again."});
      onUpdateFile(file.id, {}, { [updateLoadingField]: false });
      return;
    }

    onUpdateFile(file.id, {}, { [updateLoadingField]: true });
    const usageResult = await checkAndIncrementUsage(user.uid, usageType);

    if (!usageResult.allowed) {
      onUpdateFile(file.id, {}, { [updateLoadingField]: false });
      toast({
        variant: "destructive",
        title: "Limit Reached",
        description: usageResult.message || `Daily limit & addons used for ${usageType}. Consider upgrading or buying more addons.`,
        action: (
          <Button variant="outline" size="sm" asChild>
            <Link href="/pricing">View Plans/Addons</Link>
          </Button>
        ),
        duration: 10000,
      });
      return;
    }

    let contentForAI = file.documentContentPreview;
    if (contentForAI.length > MAX_AI_INPUT_CHARS) {
      contentForAI = contentForAI.substring(0, MAX_AI_INPUT_CHARS);
      console.warn(`AI input for ${file.name} was truncated to ${MAX_AI_INPUT_CHARS} characters.`);
    }
    if (!contentForAI.trim()) {
        toast({
            variant: "destructive",
            title: "No Content for AI",
            description: `The document ${file.name} appears to have no text content (or was truncated to empty) for AI suggestions.`,
        });
        onUpdateFile(file.id, {}, { [updateLoadingField]: false });
        return;
    }


    try {
      const result = await aiOperation(contentForAI);
      successCallback(result, usageResult.source);
      await refreshSubscription();
    } catch (error: any) {
      console.error(`Error suggesting ${usageType} for ${file.name}:`, error);
      const errorMessage = error.message || '';
      if (errorMessage.includes('[429 Too Many Requests]') || errorMessage.toLowerCase().includes('quota')) {
        toast({
          variant: "destructive",
          title: "AI Suggestion Limit Reached",
          description: `The AI service is currently unavailable due to high demand or quota limits for ${file.name}. Please try again later or check your AI service plan. This can happen with very large documents on the free tier.`,
          duration: 10000,
        });
      } else {
        toast({
          variant: "destructive",
          title: `${usageType.replace(/([A-Z])/g, ' $1')} Suggestion Error`,
          description: `Could not suggest ${usageType.toLowerCase().replace('suggestions', ' suggestions')} for ${file.name}. ${errorMessage}`,
        });
      }
    } finally {
        onUpdateFile(file.id, {}, { [updateLoadingField]: false });
    }
  }, [user, loadingSubscription, subscription, toast, onUpdateFile, refreshSubscription]);


  const handleSuggestKeywords = useCallback(async (file: PdfFile) => {
    await handleGenericAISuggestion(
      file,
      'keywordSuggestions',
      (truncatedContent) => suggestKeywords({ documentContent: truncatedContent }),
      'isLoadingKeywords',
      (result, usageSource) => {
        onUpdateFile(file.id, {}, { suggestedKeywords: result.keywords });
        toast({
          title: "Keywords Suggested",
          description: `Found ${result.keywords.length} keywords for ${file.name}. (Used ${usageSource === 'addon' ? 'addon credit' : 'daily allowance'})`,
        });
      }
    );
  }, [handleGenericAISuggestion, onUpdateFile, toast]);

  const handleSuggestSubject = useCallback(async (file: PdfFile) => {
     await handleGenericAISuggestion(
      file,
      'subjectSuggestions',
      (truncatedContent) => suggestSubject({ documentContent: truncatedContent }),
      'isLoadingSubject',
      (result, usageSource) => {
        onUpdateFile(file.id, { subject: result.suggestedSubject }, {});
        toast({
          title: "Subject Suggested",
          description: `New subject suggested for ${file.name}. (Used ${usageSource === 'addon' ? 'addon credit' : 'daily allowance'})`,
        });
      }
    );
  }, [handleGenericAISuggestion, onUpdateFile, toast]);

  const seoTooltips = useMemo(() => ({
    title: "Optimal title length is 50-60 characters. Make it descriptive and include primary keywords.",
    subject: "Subject (or description) should be around 150-160 characters. Summarize the document and include relevant keywords. Click 'Suggest' for an AI-powered idea!",
    keywords: "Use a mix of broad and specific keywords, comma-separated. Aim for 5-10 impactful keywords. Click 'Suggest Keywords' for AI help."
  }), []);


  return (
    <div className="space-y-1">
      {files.length > 0 && (
        <div className="flex items-center p-4 bg-card rounded-lg shadow-sm border border-border mb-3">
          <Checkbox
            id="select-all-checkbox"
            checked={files.length > 0 && selectedFileIds.size === files.length}
            onCheckedChange={(checked) => {
              files.forEach(file => onSelectFile(file.id, !!checked));
            }}
            aria-label="Select all files"
            className="mr-3"
          />
          <Label htmlFor="select-all-checkbox" className="font-medium text-sm text-foreground cursor-pointer">
            Select All ({selectedFileIds.size} / {files.length} selected)
          </Label>
        </div>
      )}

      <Accordion
        type="multiple"
        value={openAccordionItems}
        onValueChange={onOpenAccordionItemsChange}
        className="w-full space-y-2"
      >
        {files.map((file) => (
          <AccordionItem
            key={file.id}
            value={file.id}
            className="group border border-border rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 bg-card hover:bg-muted/30 data-[state=open]:bg-muted/50"
          >
            <div className="flex w-full items-start p-4">
              <Checkbox
                id={`select-outer-${file.id}`}
                checked={selectedFileIds.has(file.id)}
                onCheckedChange={(checked) => {
                  onSelectFile(file.id, !!checked);
                }}
                onClick={(e) => e.stopPropagation()}
                aria-label={`Select file ${file.name}`}
                className="mr-3 shrink-0 mt-1"
              />
              <AccordionTrigger
                asChild
                className="flex-1 min-w-0 p-0" 
              >
                <div className="flex items-center justify-between w-full text-left hover:no-underline cursor-pointer">
                  <span className="text-lg font-semibold text-primary break-words min-w-0" title={file.name}>
                    {file.name}
                  </span>
                  <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200 group-data-[state=open]:rotate-180 ml-2 text-muted-foreground" />
                </div>
              </AccordionTrigger>
            </div>
            <AccordionContent className="pt-0 pb-4 px-4">
              <FileMetadataCard
                file={file}
                onUpdateFile={onUpdateFile}
                isSeoView={isSeoView}
                handleSuggestKeywords={handleSuggestKeywords}
                handleSuggestSubject={handleSuggestSubject}
                seoTooltips={seoTooltips}
              />
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>

      {files.length === 0 && (
        <p className="text-center text-muted-foreground py-8">No files uploaded yet. Drag and drop PDF files above to get started.</p>
      )}
    </div>
  );
};

export default MetadataDisplayTable;

