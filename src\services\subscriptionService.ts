
// src/services/subscriptionService.ts
'use client';
import { firestore } from '@/lib/firebase';
import { doc, getDoc, updateDoc, type Timestamp, increment } from 'firebase/firestore';
import type { UserSubscriptionData, PlanLimits, AddonType, AddonPack } from '@/types';
import { PLANS, DEFAULT_PLAN_ID, ADDON_PACKS } from '@/config/plans';

function getTodayDateString(): string {
  return new Date().toISOString().split('T')[0]; // YYYY-MM-DD
}

export async function getUserSubscription(userId: string): Promise<UserSubscriptionData | null> {
  if (!userId) return null;
  const userRef = doc(firestore, 'users', userId);
  const userSnap = await getDoc(userRef);
  const today = getTodayDateString();

  if (userSnap.exists() && userSnap.data()?.subscription) {
    const sub = userSnap.data().subscription as UserSubscriptionData;
    // Ensure dailyUsage and addons structure
    if (!sub.dailyUsage || !sub.dailyUsage.keywordSuggestions || !sub.dailyUsage.subjectSuggestions) {
        sub.dailyUsage = { 
            keywordSuggestions: { count: 0, lastResetDate: today },
            subjectSuggestions: { count: 0, lastResetDate: today },
        };
    }
    if (!sub.addons) {
        sub.addons = { keywordSuggestions: 0, subjectSuggestions: 0 };
    }
    return sub;
  }
  return { // Default structure if not found (should be rare post-init)
    planId: DEFAULT_PLAN_ID,
    status: 'active',
    dailyUsage: {
      keywordSuggestions: { count: 0, lastResetDate: today },
      subjectSuggestions: { count: 0, lastResetDate: today },
    },
    addons: { keywordSuggestions: 0, subjectSuggestions: 0 },
  };
}

export type AiUsageFeature = keyof UserSubscriptionData['dailyUsage']; // This also matches AddonType

export async function checkAndIncrementUsage(
  userId: string,
  usageType: AiUsageFeature
): Promise<{
  allowed: boolean;
  message?: string;
  source?: 'daily' | 'addon'; // To indicate where the usage was drawn from
  remainingDaily?: number;
  remainingAddons?: number;
  limit?: number;
  planId?: string;
}> {
  if (!userId) return { allowed: false, message: 'User not authenticated for usage check.' };

  const userRef = doc(firestore, 'users', userId);
  const userSnap = await getDoc(userRef);

  if (!userSnap.exists() || !userSnap.data()?.subscription) {
    return { allowed: false, message: 'Subscription data not found. Please try logging in again.' };
  }

  const subscription = userSnap.data().subscription as UserSubscriptionData;
  const planFromFileConfig = PLANS.find(p => p.id === subscription.planId) || PLANS.find(p => p.id === DEFAULT_PLAN_ID)!;
  const limits = planFromFileConfig.limits;
  const today = getTodayDateString();

  // Ensure structures exist (should be handled by userService, but good for safety)
  subscription.dailyUsage = subscription.dailyUsage || {
    keywordSuggestions: { count: 0, lastResetDate: today },
    subjectSuggestions: { count: 0, lastResetDate: today },
  };
  subscription.addons = subscription.addons || { keywordSuggestions: 0, subjectSuggestions: 0 };
  
  if (!subscription.dailyUsage[usageType]) {
    subscription.dailyUsage[usageType] = { count: 0, lastResetDate: today };
  }


  let currentDailyUsageStats = subscription.dailyUsage[usageType];

  if (currentDailyUsageStats.lastResetDate !== today) {
    currentDailyUsageStats = { count: 0, lastResetDate: today };
    // No need to update Firestore here yet, will be part of the increment update if allowed
  }

  let limitForFeature: number;
  switch (usageType) {
    case 'keywordSuggestions':
      limitForFeature = limits.keywordSuggestionsPerDay;
      break;
    case 'subjectSuggestions':
      limitForFeature = limits.subjectSuggestionsPerDay;
      break;
    default:
      return { allowed: false, message: 'Unknown AI usage type.' };
  }

  // Try daily limit first
  if (currentDailyUsageStats.count < limitForFeature) {
    currentDailyUsageStats.count += 1;
    const updatePath = `subscription.dailyUsage.${usageType}`;
    try {
      await updateDoc(userRef, { [updatePath]: currentDailyUsageStats });
      return {
        allowed: true,
        source: 'daily',
        remainingDaily: limitForFeature - currentDailyUsageStats.count,
        limit: limitForFeature,
        planId: planFromFileConfig.id,
      };
    } catch (error) {
      console.error(`Error updating daily ${usageType} usage for ${userId}:`, error);
      return { allowed: false, message: `Failed to update daily ${usageType} usage.` };
    }
  }

  // Daily limit reached, try addons
  let currentAddonCount = subscription.addons[usageType] || 0;
  if (currentAddonCount > 0) {
    currentAddonCount -= 1;
    const updatePath = `subscription.addons.${usageType}`;
    try {
      await updateDoc(userRef, { [updatePath]: currentAddonCount });
      return {
        allowed: true,
        source: 'addon',
        remainingAddons: currentAddonCount,
        planId: planFromFileConfig.id,
      };
    } catch (error) {
      console.error(`Error updating addon ${usageType} usage for ${userId}:`, error);
      return { allowed: false, message: `Failed to update addon ${usageType} usage.` };
    }
  }

  // Both daily and addon limits reached
  const featureName = usageType.replace(/([A-Z])/g, ' $1').toLowerCase();
  return {
    allowed: false,
    message: `Daily limit of ${limitForFeature} ${featureName} and all addon packs used for the ${planFromFileConfig.name} plan.`,
    remainingDaily: 0,
    remainingAddons: 0,
    limit: limitForFeature,
    planId: planFromFileConfig.id,
  };
}

export async function upgradeSubscriptionDemo(userId: string, newPlanId: string): Promise<{ success: boolean; message: string }> {
  if (!userId || !newPlanId) {
    return { success: false, message: "User ID and New Plan ID are required." };
  }
  const userRef = doc(firestore, 'users', userId);
  const today = getTodayDateString();
  const newPlan = PLANS.find(p => p.id === newPlanId);

  if (!newPlan) {
    return { success: false, message: "Invalid new plan ID." };
  }

  try {
    const newSubscriptionDataPartial: any = { // eslint-disable-line @typescript-eslint/no-explicit-any
      'subscription.planId': newPlanId,
      'subscription.status': 'active',
      'subscription.currentPeriodEnd': null,
      'subscription.dailyUsage.keywordSuggestions': { count: 0, lastResetDate: today },
      'subscription.dailyUsage.subjectSuggestions': { count: 0, lastResetDate: today },
    };
    // If upgrading to Pro, existing addons might be less relevant, or you might want to keep them.
    // For this demo, we'll leave addons as they are.
    await updateDoc(userRef, newSubscriptionDataPartial);
    console.log(`[Demo] User ${userId} 'upgraded' to plan ${newPlanId}.`);
    return { success: true, message: `Successfully subscribed to ${newPlan.name} plan (Demo mode).` };
  } catch (error) {
    console.error(`Error in demo upgrade for user ${userId} to plan ${newPlanId}:`, error);
    return { success: false, message: 'Failed to update plan in demo mode.' };
  }
}

export async function purchaseAddonDemo(userId: string, addonPackId: string): Promise<{ success: boolean; message: string }> {
  if (!userId || !addonPackId) {
    return { success: false, message: "User ID and Addon Pack ID are required." };
  }
  const userRef = doc(firestore, 'users', userId);
  const addonPack = ADDON_PACKS.find(p => p.id === addonPackId);

  if (!addonPack) {
    return { success: false, message: "Invalid addon pack ID." };
  }

  try {
    // Increment the specific addon type quantity
    const updatePath = `subscription.addons.${addonPack.type}`;
    await updateDoc(userRef, {
      [updatePath]: increment(addonPack.quantity)
    });
    console.log(`[Demo] User ${userId} 'purchased' addon pack ${addonPackId} (+${addonPack.quantity} ${addonPack.type}).`);
    return { success: true, message: `Successfully added ${addonPack.name} (Demo mode).` };
  } catch (error) {
    console.error(`Error in demo addon purchase for user ${userId}, pack ${addonPackId}:`, error);
    return { success: false, message: 'Failed to add addon pack in demo mode.' };
  }
}
