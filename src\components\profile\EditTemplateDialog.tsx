
"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from '@/components/ui/textarea';
import type { MetadataTemplate, CreateTemplateData } from '@/types';
import { useToast } from "@/hooks/use-toast";

interface EditTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (templateData: CreateTemplateData, templateId?: string) => Promise<MetadataTemplate | null>;
  existingTemplate?: MetadataTemplate | null; // Provide for editing, null/undefined for creating
}

const EditTemplateDialog: React.FC<EditTemplateDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  existingTemplate,
}) => {
  const [name, setName] = useState("");
  const [title, setTitle] = useState("");
  const [author, setAuthor] = useState("");
  const [subject, setSubject] = useState("");
  const [keywords, setKeywords] = useState("");
  const [producer, setProducer] = useState("");
  const [creatorTool, setCreatorTool] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const { toast } = useToast();

  useEffect(() => {
    if (isOpen) {
      if (existingTemplate) {
        setName(existingTemplate.name);
        setTitle(existingTemplate.title || "");
        setAuthor(existingTemplate.author || "");
        setSubject(existingTemplate.subject || "");
        setKeywords(existingTemplate.keywords || "");
        setProducer(existingTemplate.producer || "");
        setCreatorTool(existingTemplate.creatorTool || "");
      } else {
        // Reset for "create" mode
        setName("");
        setTitle("");
        setAuthor("");
        setSubject("");
        setKeywords("");
        setProducer("");
        setCreatorTool("");
      }
      setIsSaving(false);
    }
  }, [isOpen, existingTemplate]);

  const handleSubmit = async () => {
    if (!name.trim()) {
      toast({ title: "Template Name Required", description: "Please enter a name for the template.", variant: "destructive" });
      return;
    }
    setIsSaving(true);
    const templateData: CreateTemplateData = {
      name: name.trim(),
      title: title.trim(),
      author: author.trim(),
      subject: subject.trim(),
      keywords: keywords.trim(),
      producer: producer.trim(),
      creatorTool: creatorTool.trim(),
    };

    const savedTemplate = await onSave(templateData, existingTemplate?.id);
    setIsSaving(false);
    if (savedTemplate) {
      onClose();
    }
    // Toast notifications are handled by the caller (ProfilePage)
  };

  const dialogTitle = existingTemplate ? "Edit Metadata Template" : "Create New Metadata Template";
  const dialogDescription = existingTemplate
    ? `Editing template: ${existingTemplate.name}`
    : "Define the fields for your new reusable template.";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-card text-card-foreground">
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
          <DialogDescription>{dialogDescription}</DialogDescription>
        </DialogHeader>

        <div className="py-4 grid grid-cols-1 gap-4 max-h-[60vh] overflow-y-auto pr-2">
          <div className="space-y-1">
            <Label htmlFor="template-name">Template Name (Required)</Label>
            <Input id="template-name" value={name} onChange={e => setName(e.target.value)} placeholder="e.g., Company Standard" disabled={isSaving}/>
          </div>
          <div className="space-y-1">
            <Label htmlFor="template-title">Title</Label>
            <Input id="template-title" value={title} onChange={e => setTitle(e.target.value)} placeholder="Default Title (Optional)" disabled={isSaving}/>
          </div>
          <div className="space-y-1">
            <Label htmlFor="template-author">Author</Label>
            <Input id="template-author" value={author} onChange={e => setAuthor(e.target.value)} placeholder="Default Author (Optional)" disabled={isSaving}/>
          </div>
          <div className="space-y-1">
            <Label htmlFor="template-subject">Subject / Description</Label>
            <Textarea id="template-subject" value={subject} onChange={e => setSubject(e.target.value)} placeholder="Default Subject (Optional)" className="min-h-[60px]" disabled={isSaving}/>
          </div>
          <div className="space-y-1">
            <Label htmlFor="template-keywords">Keywords (comma-separated)</Label>
            <Textarea id="template-keywords" value={keywords} onChange={e => setKeywords(e.target.value)} placeholder="e.g., keyword1, keyword2 (Optional)" className="min-h-[60px]" disabled={isSaving}/>
          </div>
          <div className="space-y-1">
            <Label htmlFor="template-producer">Producer</Label>
            <Input id="template-producer" value={producer} onChange={e => setProducer(e.target.value)} placeholder="Default Producer (Optional)" disabled={isSaving}/>
          </div>
          <div className="space-y-1">
            <Label htmlFor="template-creatorTool">Creator Tool</Label>
            <Input id="template-creatorTool" value={creatorTool} onChange={e => setCreatorTool(e.target.value)} placeholder="Default Creator Tool (Optional)" disabled={isSaving}/>
          </div>
        </div>

        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button variant="outline" disabled={isSaving}>Cancel</Button>
          </DialogClose>
          <Button onClick={handleSubmit} disabled={isSaving || !name.trim()}>
            {isSaving ? (existingTemplate ? "Updating..." : "Creating...") : (existingTemplate ? "Update Template" : "Create Template")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditTemplateDialog;
