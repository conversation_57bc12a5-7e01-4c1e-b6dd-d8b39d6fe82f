
// src/app/pricing/page.tsx
'use client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle2, GiftIcon, Loader2, ShoppingCart } from 'lucide-react';
import { PLANS, ADDON_PACKS } from '@/config/plans';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
// upgradeSubscriptionDemo and purchaseAddonDemo are kept for potential future use or direct non-LS demos
// import { upgradeSubscriptionDemo, purchaseAddonDemo } from '@/services/subscriptionService'; 
// import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';
import React from 'react';

export default function PricingPage() {
  const { user, loading: authLoading } = useAuth();
  const { subscription, loadingSubscription } = useSubscription();
  // const { toast } = useToast();
  // const [isProcessing, setIsProcessing] = useState<string | null>(null);

  const getCheckoutUrlWithUserId = (baseUrl: string): string => {
    if (!user || !user.uid) return baseUrl; // Or handle login prompt
    // Ensure the base URL doesn't already have query params in a way that breaks this.
    // Basic check for existing '?'
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}checkout[custom][user_id]=${user.uid}`;
  };


  if (authLoading || loadingSubscription) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading pricing information...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-extrabold tracking-tight sm:text-5xl text-primary">
          Choose Your Plan
        </h1>
        <p className="mt-4 text-xl text-muted-foreground max-w-2xl mx-auto">
          Unlock more features with our Pro plan, get started for free, or top up with suggestion packs.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {PLANS.map((plan) => (
          <Card 
            key={plan.id} 
            className={`flex flex-col shadow-lg rounded-xl overflow-hidden transition-all duration-300 hover:shadow-2xl 
 ${subscription?.planId === plan.id && subscription?.status === 'active' ? 'border-2 border-primary ring-2 ring-primary shadow-primary/30' : plan.id === 'pro' ? 'border-secondary' : 'border-border'}`}
          >
            <CardHeader className="bg-card p-6">
              <CardTitle className="text-3xl font-bold text-card-foreground">{plan.name}</CardTitle>
              <CardDescription className="mt-2 text-muted-foreground min-h-[4em]">{plan.description}</CardDescription>
              <div className="mt-6">
                <span className="text-5xl font-extrabold text-foreground">£{plan.priceMonthly}</span>
                <span className="text-lg font-medium text-muted-foreground">/month</span>
              </div>
            </CardHeader>
            <CardContent className="p-6 flex-grow">
 <h3 className={`text-sm font-semibold uppercase tracking-wider mb-4 ${plan.id === 'pro' ? 'text-secondary' : 'text-muted-foreground'}`}>What&apos;s included</h3>
              <ul className="space-y-3">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle2 className="flex-shrink-0 h-5 w-5 text-accent mr-2 mt-0.5" />
                    <span className="text-sm text-foreground">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter className="p-6 bg-card/50 border-t border-border mt-auto">
              {user ? (
                subscription?.planId === plan.id && subscription?.status === 'active' ? (
                  <Button className="w-full py-3 text-base" variant="outline" disabled>
                    Current Plan
                  </Button>
                ) : (
                  plan.lemonSqueezyCheckoutUrl ? (
                    <Button 
                      className="w-full py-3 text-base" 
                      asChild
                      variant={plan.id === 'pro' ? 'default' : 'outline'}

                    >
                      <Link href={getCheckoutUrlWithUserId(plan.lemonSqueezyCheckoutUrl)}>

                        {subscription?.planId === 'free' && plan.id === 'pro' ? 'Upgrade to Pro' : (plan.id === 'free' ? 'Select Free (No Payment)' : `Get ${plan.name}`)}
                      </Link>
                    </Button>
                  ) : (
                     <Button className="w-full py-3 text-base" variant="outline" disabled>
                        Coming Soon
                     </Button>
                  )
                )
              ) : (
                plan.lemonSqueezyCheckoutUrl ? (
                    <Button className="w-full py-3 text-base" asChild>
                        <Link href="/login">Login to Subscribe</Link>
                    </Button>
                ) : (
                    <Button className="w-full py-3 text-base" variant="outline" disabled>
                        Login to Subscribe
                    </Button>
                )
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="mt-16 text-center">
        <h2 className="text-3xl font-extrabold tracking-tight text-primary mb-8">
          Need More Suggestions?
        </h2>
        <p className="mt-2 mb-8 text-lg text-muted-foreground max-w-xl mx-auto">
          Top up your AI suggestions with these one-time purchase packs.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
        {ADDON_PACKS.map((pack) => (
          <Card key={pack.id} className="flex flex-col shadow-md rounded-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
            <CardHeader className="bg-card p-5">
              <div className="flex items-center justify-center mb-3">
                <GiftIcon className="h-10 w-10 text-accent" />
              </div>
              <CardTitle className="text-xl font-semibold text-card-foreground text-center">{pack.name}</CardTitle>
              <CardDescription className="mt-1 text-muted-foreground text-sm text-center min-h-[2.5em]">{pack.description}</CardDescription>
            </CardHeader>
            <CardContent className="p-5 flex-grow">
              <div className="text-center mb-3">
                <span className="text-3xl font-bold text-foreground">£{pack.price.toFixed(2)}</span>
              </div>
              <p className="text-sm text-muted-foreground text-center">
                Get <span className="font-semibold text-foreground">{pack.quantity}</span> extra {pack.type === 'keywordSuggestions' ? 'keyword' : 'subject'} suggestions.
              </p>
            </CardContent>
            <CardFooter className="p-5 bg-card/50 border-t border-border mt-auto">
              {user ? (
                pack.lemonSqueezyCheckoutUrl ? (
                  <Button 
                    className="w-full" 
                    asChild

                  >
                    <Link href={getCheckoutUrlWithUserId(pack.lemonSqueezyCheckoutUrl)}>
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      Buy Pack
                    </Link>
                  </Button>
                ) : (
                   <Button className="w-full" variant="outline" disabled>Coming Soon</Button>
                )
              ) : (
                 pack.lemonSqueezyCheckoutUrl ? (
                    <Button className="w-full" asChild>
                      <Link href="/login">Login to Buy</Link>
                    </Button>
                 ) : (
                    <Button className="w-full" variant="outline" disabled>Login to Buy</Button>
                 )
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

       <div className="mt-16 text-center text-sm text-muted-foreground">
        <p>Payments are processed securely via Lemon Squeezy. For Pro plan, your subscription would auto-renew monthly unless canceled. Add-on packs are one-time purchases.</p>
        <p className="font-semibold mt-2">Make sure to replace placeholder Lemon Squeezy URLs and Variant IDs in the code.</p>
      </div>
    </div>
  );
}
