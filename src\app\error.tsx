'use client';

import { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Global Error Page for Next.js App Router
 * 
 * This component is automatically used by Next.js when an error occurs
 * in any page or layout. It provides a user-friendly error interface
 * with recovery options.
 */
export default function ErrorPage({ error, reset }: ErrorPageProps) {
  useEffect(() => {
    // Log the error to the console and external services
    console.error('[Global Error Page] Application error:', error);
    
    // Log error details for debugging
    const errorData = {
      message: error.message,
      stack: error.stack,
      digest: error.digest,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
    };

    console.error('[Global Error Page] Error details:', errorData);

    // TODO: Send to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { extra: errorData });
    }
  }, [error]);

  const handleGoHome = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  };

  const handleReportError = () => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      digest: error.digest,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
    };

    // Copy error report to clipboard
    if (typeof window !== 'undefined' && navigator.clipboard) {
      navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
        .then(() => {
          alert('Error report copied to clipboard. Please share this with support.');
        })
        .catch(() => {
          console.error('Failed to copy error report to clipboard');
        });
    }
  };

  const getErrorMessage = (): { title: string; description: string } => {
    const message = error.message.toLowerCase();
    
    if (message.includes('chunk') || message.includes('loading')) {
      return {
        title: 'Loading Error',
        description: 'There was a problem loading part of the application. This usually resolves itself when you refresh the page.',
      };
    }
    
    if (message.includes('network') || message.includes('fetch')) {
      return {
        title: 'Network Error',
        description: 'Unable to connect to our servers. Please check your internet connection and try again.',
      };
    }
    
    if (message.includes('auth') || message.includes('unauthorized')) {
      return {
        title: 'Authentication Error',
        description: 'There was a problem with your authentication. Please try logging in again.',
      };
    }

    return {
      title: 'Application Error',
      description: 'The application encountered an unexpected error. Our team has been notified and is working on a fix.',
    };
  };

  const { title, description } = getErrorMessage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-16 w-16 text-destructive">
            <AlertTriangle className="h-full w-full" />
          </div>
          <CardTitle className="text-3xl">{title}</CardTitle>
          <CardDescription className="text-lg">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error.digest && (
                <>
                  Error ID: <code className="text-sm font-mono">{error.digest}</code>
                </>
              )}
              {!error.digest && (
                <>
                  Error: <code className="text-sm font-mono">{error.message}</code>
                </>
              )}
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <h3 className="text-lg font-semibold">What you can try:</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Refresh the page to reload the application</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Check your internet connection</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Clear your browser cache and cookies</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Try using a different browser or incognito mode</span>
              </li>
            </ul>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <details className="text-sm">
              <summary className="cursor-pointer font-medium text-destructive">
                Developer Information (Development Only)
              </summary>
              <pre className="mt-2 overflow-auto rounded bg-muted p-3 text-xs">
                {error.stack}
              </pre>
            </details>
          )}

          <div className="flex flex-col gap-3 sm:flex-row">
            <Button onClick={reset} className="flex-1">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button onClick={handleGoHome} variant="outline" className="flex-1">
              <Home className="mr-2 h-4 w-4" />
              Go to Homepage
            </Button>
            <Button onClick={handleReportError} variant="outline" className="flex-1">
              <Bug className="mr-2 h-4 w-4" />
              Report Error
            </Button>
          </div>

          <div className="text-center text-sm text-muted-foreground">
            <p>
              If the problem persists, please contact our support team at{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
