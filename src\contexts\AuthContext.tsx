
'use client';

import type { User } from 'firebase/auth';
import {
  createUserWithEmailAndPassword,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
} from 'firebase/auth';
import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useState } from 'react';
import { auth } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';
import { initializeUserDocument } from '@/services/userService';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email_input: string, password_input: string) => Promise<void>;
  signup: (email_input: string, password_input: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      if (currentUser) {
        setUser(currentUser);
        // Initialize user document in Firestore but don't await it here
        // to prevent blocking setLoading(false). This can run in the background.
        initializeUserDocument(currentUser.uid, currentUser.email)
          .catch(error => {
            console.error(`[AuthContext] Background error during initializeUserDocument for UID ${currentUser.uid}:`, error);
            // Optionally, you could add a non-critical toast here if needed
          });
      } else {
        setUser(null);
      }
      setLoading(false); // setLoading(false) now happens much sooner
    });
    return () => {
      unsubscribe();
    }
  }, []);

  const login = async (email_input: string, password_input: string) => {
    try {
      await signInWithEmailAndPassword(auth, email_input, password_input);
      // initializeUserDocument will be triggered by onAuthStateChanged
      toast({ title: 'Logged In', description: 'Welcome back!' });
    } catch (error: unknown) {
      console.error('[AuthContext] Login error:', error);
      toast({
        variant: 'destructive',
        title: 'Login Failed',
        description: error instanceof Error ? error.message : 'Could not log in. Please check your credentials.',
      });
      throw error;
    }
  };

  const signup = async (email_input: string, password_input: string) => {
    try {
      await createUserWithEmailAndPassword(auth, email_input, password_input);
      // initializeUserDocument will be triggered by onAuthStateChanged
      toast({ title: 'Signed Up', description: 'Welcome to MetaPDF!' });
    } catch (error: unknown) {
      console.error('[AuthContext] Signup error:', error);
      toast({
        variant: 'destructive',
        title: 'Signup Failed',
        description: error instanceof Error ? error.message : 'Could not sign up. Please try again.',
      });
      throw error;
    }
  };

  const logout = async () => {
    try {
      await firebaseSignOut(auth);
      toast({ title: 'Logged Out', description: 'You have been successfully logged out.' });
    } catch (error: unknown)
{
      console.error('[AuthContext] Logout error:', error);
      toast({
        variant: 'destructive',
        title: 'Logout Failed',
        description: error instanceof Error ? error.message : 'Could not log out. Please try again.',
      });
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, signup, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
