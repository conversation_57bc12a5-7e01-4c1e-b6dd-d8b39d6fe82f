# Deployment Guide

This guide covers the complete deployment process for MetaPDF, including environment setup, configuration, and production deployment strategies.

## Deployment Overview

MetaPDF is a Next.js application that can be deployed to various platforms. The recommended deployment stack includes:

- **Frontend**: Next.js application
- **Hosting**: Firebase Hosting (recommended) or Vercel
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **AI Services**: Google Generative AI
- **Payment Processing**: LemonSqueezy
- **CDN**: Firebase CDN or Cloudflare

## Prerequisites

### Required Accounts
- Firebase project with Blaze plan
- Google Cloud Platform account (for AI services)
- LemonSqueezy account (for payments)
- Domain name (for production)

### Required Tools
- Node.js 18+ and npm
- Firebase CLI
- Git
- Code editor

## Environment Configuration

### Environment Variables

Create environment files for different stages:

#### `.env.local` (Development)
```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_dev_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-dev.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-dev
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-dev.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=*********
NEXT_PUBLIC_FIREBASE_APP_ID=1:*********:web:abcdef

# Firebase Admin (Server-side)
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>

# Google AI
GOOGLE_AI_API_KEY=your_google_ai_api_key

# LemonSqueezy
LEMON_SQUEEZY_SIGNING_SECRET=your_webhook_secret

# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:9002
```

#### `.env.production` (Production)
```bash
# Firebase Configuration (Production)
NEXT_PUBLIC_FIREBASE_API_KEY=your_prod_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=987654321
NEXT_PUBLIC_FIREBASE_APP_ID=1:987654321:web:fedcba

# Firebase Admin (Production)
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>

# Google AI (Production)
GOOGLE_AI_API_KEY=your_prod_google_ai_api_key

# LemonSqueezy (Production)
LEMON_SQUEEZY_SIGNING_SECRET=your_prod_webhook_secret

# Application (Production)
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://www.metapdf.app
```

### Security Considerations

1. **Never commit secrets to version control**
2. **Use different keys for development and production**
3. **Rotate keys regularly**
4. **Use environment-specific Firebase projects**
5. **Enable Firebase security rules**

## Firebase Setup

### 1. Create Firebase Projects

Create separate Firebase projects for development and production:

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in your project
firebase init
```

Select the following services:
- Firestore Database
- Functions (optional)
- Hosting
- Storage (optional)

### 2. Configure Firestore Security Rules

Create `firestore.rules`:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // User subscriptions
    match /subscriptions/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Templates (user-specific)
    match /templates/{templateId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
  }
}
```

### 3. Configure Firebase Hosting

Create `firebase.json`:

```json
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "hosting": {
    "public": "out",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "public, max-age=31536000, immutable"
          }
        ]
      }
    ]
  }
}
```

## Build Process

### Development Build

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run type checking
npm run typecheck

# Run linting
npm run lint
```

### Production Build

```bash
# Install dependencies
npm ci --only=production

# Build the application
npm run build

# Test the production build locally
npm start
```

### Build Optimization

The build process includes:
- TypeScript compilation
- Code minification and bundling
- Image optimization
- Static generation for public pages
- Service worker generation (PWA)

## Deployment Strategies

### Option 1: Firebase Hosting (Recommended)

#### Automatic Deployment with GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Firebase Hosting

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          NEXT_PUBLIC_FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          # ... other environment variables
      
      - name: Deploy to Firebase
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
          channelId: live
          projectId: your-project-id
```

#### Manual Deployment

```bash
# Build the application
npm run build

# Deploy to Firebase
firebase deploy --only hosting

# Deploy with specific project
firebase deploy --only hosting --project your-project-id
```

### Option 2: Vercel Deployment

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel

# Deploy to production
vercel --prod
```

Create `vercel.json`:

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "env": {
    "NEXT_PUBLIC_FIREBASE_API_KEY": "@firebase-api-key",
    "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN": "@firebase-auth-domain"
  }
}
```

## Domain Configuration

### Custom Domain Setup

#### Firebase Hosting
1. Go to Firebase Console > Hosting
2. Click "Add custom domain"
3. Enter your domain name
4. Follow DNS configuration instructions
5. Wait for SSL certificate provisioning

#### DNS Configuration
```
# A Records
@ -> *************
@ -> **************

# CNAME Record
www -> your-project.web.app
```

### SSL Certificate

Firebase Hosting automatically provisions SSL certificates for custom domains. For other hosting providers:

1. **Let's Encrypt**: Free SSL certificates
2. **Cloudflare**: SSL proxy with additional security
3. **Custom Certificate**: Upload your own certificate

## Monitoring and Logging

### Application Monitoring

#### Firebase Analytics
```typescript
// Initialize Analytics
import { analytics } from '@/lib/firebase';
import { logEvent } from 'firebase/analytics';

// Track custom events
logEvent(analytics, 'file_upload', {
  file_size: fileSize,
  file_type: 'pdf'
});
```

#### Error Tracking

Integrate with error tracking services:

```typescript
// Sentry integration
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
});
```

### Performance Monitoring

#### Web Vitals Tracking
```typescript
// pages/_app.tsx
export function reportWebVitals(metric) {
  if (analytics) {
    logEvent(analytics, 'web_vitals', {
      name: metric.name,
      value: metric.value,
    });
  }
}
```

#### Lighthouse CI
```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI
on: [push]
jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Audit URLs using Lighthouse
        uses: treosh/lighthouse-ci-action@v9
        with:
          urls: |
            https://www.metapdf.app
            https://www.metapdf.app/pricing
          configPath: './lighthouserc.json'
```

## Backup and Recovery

### Database Backup

```bash
# Export Firestore data
gcloud firestore export gs://your-backup-bucket/backup-$(date +%Y%m%d)

# Schedule automated backups
gcloud scheduler jobs create app-engine backup-firestore \
  --schedule="0 2 * * *" \
  --target-service=backup-service
```

### Code Backup

- **Git Repository**: Primary source control
- **GitHub**: Remote repository with branch protection
- **Automated Backups**: Daily repository snapshots

## Security Checklist

### Pre-Deployment Security

- [ ] Environment variables secured
- [ ] Firebase security rules configured
- [ ] HTTPS enforced
- [ ] CSP headers implemented
- [ ] Input validation in place
- [ ] Error handling secure
- [ ] Dependencies updated
- [ ] Security headers configured

### Post-Deployment Security

- [ ] SSL certificate active
- [ ] Security headers verified
- [ ] Firestore rules tested
- [ ] Authentication working
- [ ] API endpoints secured
- [ ] Monitoring configured
- [ ] Backup procedures tested

## Troubleshooting

### Common Deployment Issues

#### Build Failures
```bash
# Clear Next.js cache
rm -rf .next

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Environment Variable Issues
- Verify all required variables are set
- Check variable names (case-sensitive)
- Ensure NEXT_PUBLIC_ prefix for client-side variables
- Restart development server after changes

#### Firebase Deployment Issues
```bash
# Check Firebase CLI version
firebase --version

# Login again
firebase logout
firebase login

# Check project configuration
firebase projects:list
firebase use your-project-id
```

### Performance Issues

#### Bundle Size Optimization
```bash
# Analyze bundle size
npm run build
npx @next/bundle-analyzer
```

#### Image Optimization
- Use Next.js Image component
- Optimize images before upload
- Configure proper caching headers

---

This deployment guide ensures a smooth and secure deployment process for MetaPDF across different environments and platforms.
