# MetaPDF Documentation

Welcome to the MetaPDF documentation! This comprehensive guide covers everything you need to know about using, developing, and maintaining the MetaPDF application.

## 📚 Documentation Structure

### User Documentation
- [User Guide](./user-guide/README.md) - Complete guide for end users
- [Features Overview](./user-guide/features.md) - Detailed feature descriptions
- [FAQ](./user-guide/faq.md) - Frequently asked questions
- [Troubleshooting](./user-guide/troubleshooting.md) - Common issues and solutions

### Developer Documentation
- [Development Setup](./development/setup.md) - Getting started with development
- [Architecture Overview](./development/architecture.md) - System architecture and design
- [API Documentation](./development/api.md) - API endpoints and usage
- [Component Documentation](./development/components.md) - React component reference
- [Error Handling](./development/error-handling.md) - Error boundary and handling system
- [Security](./development/security.md) - Security measures and best practices

### Deployment & Operations
- [Deployment Guide](./deployment/README.md) - Production deployment instructions
- [Environment Configuration](./deployment/environment.md) - Environment variables and configuration
- [Monitoring & Logging](./deployment/monitoring.md) - Application monitoring setup
- [Backup & Recovery](./deployment/backup.md) - Data backup and recovery procedures

### Contributing
- [Contributing Guide](./contributing/README.md) - How to contribute to the project
- [Code Style Guide](./contributing/code-style.md) - Coding standards and conventions
- [Testing Guide](./contributing/testing.md) - Testing strategies and guidelines
- [Release Process](./contributing/releases.md) - How releases are managed

## 🚀 Quick Start

### For Users
1. Visit [MetaPDF](https://www.metapdf.app)
2. Sign up for an account
3. Upload your PDF files
4. Edit metadata and optimize for SEO
5. Download your enhanced PDFs

### For Developers
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables (see [Environment Configuration](./deployment/environment.md))
4. Start development server: `npm run dev`
5. Visit `http://localhost:9002`

## 🔧 Key Features

- **Drag & Drop PDF Upload** - Easy file management
- **Metadata Editing** - Comprehensive PDF metadata editing
- **AI-Powered Suggestions** - Smart keyword and description suggestions
- **Bulk Operations** - Edit multiple files simultaneously
- **SEO Optimization** - Optimize PDFs for search engines
- **Template System** - Save and apply metadata templates
- **Export Options** - Multiple export formats (CSV, Excel)

## 🛡️ Security Features

- **Content Security Policy (CSP)** - Comprehensive CSP headers
- **Error Boundaries** - Graceful error handling and recovery
- **Authentication** - Secure Firebase authentication
- **Data Protection** - Encrypted data transmission and storage
- **Input Validation** - Comprehensive input sanitization

## 📊 Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Radix UI
- **Authentication**: Firebase Auth
- **Database**: Firestore
- **AI Services**: Google Generative AI
- **File Processing**: PDF-lib, PDF.js
- **Deployment**: Firebase Hosting

## 🆘 Support

- **Documentation**: Browse this documentation for detailed information
- **Issues**: Report bugs and feature requests on GitHub
- **Email**: Contact <NAME_EMAIL>
- **Community**: Join our community discussions

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](../LICENSE) file for details.

## 🔄 Version Information

- **Current Version**: 0.1.0
- **Node.js**: >= 18.0.0
- **Next.js**: 15.3.3
- **React**: 18.3.1

## 📈 Changelog

See [CHANGELOG.md](../CHANGELOG.md) for a detailed history of changes and updates.

---

**Last Updated**: December 2024  
**Documentation Version**: 1.0.0
