
"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { Metadata } from '@/types';

interface ExtractMetadataDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExtract: (fields: (keyof Metadata)[], format: 'csv') => void; 
  metadataFields: (keyof Metadata)[]; 
  numFiles: number;
}

const allPossibleMetadataFields: { id: keyof Metadata; label: string }[] = [
  { id: "title", label: "Title" },
  { id: "author", label: "Author" },
  { id: "subject", label: "Subject" },
  { id: "keywords", label: "Keywords" },
  { id: "producer", label: "Producer" },
  { id: "creatorTool", label: "Creator Tool" },
];

const ExtractMetadataDialog: React.FC<ExtractMetadataDialogProps> = ({ 
  isOpen, 
  onClose, 
  onExtract,
  numFiles 
}) => {
  const [selectedFields, setSelectedFields] = useState<Set<keyof Metadata>>(new Set(allPossibleMetadataFields.map(f => f.id)));
  const [format, setFormat] = useState<'csv'>('csv'); 

  useEffect(() => {
    if (isOpen) {
      // Initialize with all fields selected, reflecting the `metadataFields` prop if available,
      // otherwise default to all known fields.
      const initialSelected = new Set(allPossibleMetadataFields.map(f => f.id));
      setSelectedFields(initialSelected);
      setFormat('csv');
    }
  }, [isOpen]);

  const handleFieldToggle = (field: keyof Metadata) => {
    setSelectedFields(prev => {
      const next = new Set(prev);
      if (next.has(field)) {
        next.delete(field);
      } else {
        next.add(field);
      }
      return next;
    });
  };

  const handleSubmit = () => {
    onExtract(Array.from(selectedFields), format);
    onClose();
  };
  
  if (numFiles === 0 && isOpen) {
    onClose(); 
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-card text-card-foreground">
        <DialogHeader>
          <DialogTitle>Extract Metadata</DialogTitle>
          <DialogDescription>
            Select fields to extract for {numFiles} file(s). Data will be downloaded as a CSV file.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div>
            <Label className="text-sm font-medium">Fields to Extract</Label>
            <div className="mt-2 grid grid-cols-2 gap-2">
              {allPossibleMetadataFields.map(field => (
                <div key={field.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`extract-${field.id}`}
                    checked={selectedFields.has(field.id)}
                    onCheckedChange={() => handleFieldToggle(field.id)}
                  />
                  <Label htmlFor={`extract-${field.id}`} className="font-normal cursor-pointer">
                    {field.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
          <div>
            <Label htmlFor="extract-format" className="text-sm font-medium">Output Format</Label>
            <Select value={format} onValueChange={(value) => setFormat(value as 'csv')} disabled>
              <SelectTrigger id="extract-format" className="mt-1">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">CSV (Comma Separated Values)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button type="submit" onClick={handleSubmit} disabled={selectedFields.size === 0}>
            Extract and Download CSV
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ExtractMetadataDialog;
