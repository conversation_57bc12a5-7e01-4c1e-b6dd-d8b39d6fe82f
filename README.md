
# MetaPDF

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Next.js](https://img.shields.io/badge/Next.js-15.3.3-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![Firebase](https://img.shields.io/badge/Firebase-11.8.1-orange)](https://firebase.google.com/)

**MetaPDF** is a powerful web application for editing, optimizing, and managing PDF metadata. Built with Next.js, TypeScript, and Firebase, it provides an intuitive interface for bulk PDF metadata editing with AI-powered suggestions and SEO optimization features.

## 🚀 Features

### Core Features
- **Drag & Drop Upload** - Easy PDF file management with visual feedback
- **Metadata Editing** - Comprehensive PDF metadata editing (title, author, subject, keywords, etc.)
- **Bulk Operations** - Edit multiple files simultaneously with bulk editing tools
- **AI-Powered Suggestions** - Smart keyword and description generation using Google AI (5/day Free, 100/day Pro)
- **SEO Optimization** - Optimize PDFs for search engines with character counters and tips
- **Template System** - Save and apply metadata templates (Pro feature)
- **Export Options** - Export metadata to CSV/Excel formats
- **Add-on Packs** - Purchase additional AI suggestions as needed

### Advanced Features
- **Smart File Validation** - Intelligent file size limits with helpful warnings
- **Progress Tracking** - Visual progress indicators for all operations
- **Responsive Design** - Works seamlessly on desktop and mobile devices
- **PWA Support** - Progressive Web App with offline capabilities
- **Error Boundaries** - Comprehensive error handling with graceful recovery
- **Security Headers** - Content Security Policy and security best practices
- **Large File Support** - Handle files up to 100MB (Free) / 200MB (Pro)

## 🛡️ Security Features

- **Content Security Policy (CSP)** - Comprehensive XSS protection
- **Error Boundaries** - Multiple layers of error handling
- **Input Validation** - Comprehensive data sanitization
- **Authentication** - Secure Firebase authentication
- **HTTPS Enforcement** - Secure data transmission
- **Security Headers** - HSTS, X-Frame-Options, and more

## 🏗️ Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Radix UI Components
- **Authentication**: Firebase Auth
- **Database**: Firebase Firestore
- **AI Services**: Google Generative AI
- **File Processing**: PDF-lib, PDF.js
- **Payment**: LemonSqueezy integration
- **Deployment**: Firebase Hosting

## 📋 Prerequisites

- Node.js 18+ and npm
- Firebase project with Blaze plan
- Google Cloud Platform account (for AI services)
- LemonSqueezy account (for payments)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/metapdf.git
cd metapdf
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Setup

Create a `.env.local` file in the root directory:

```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=*********
NEXT_PUBLIC_FIREBASE_APP_ID=1:*********:web:abcdef

# Firebase Admin (Server-side)
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>

# Google AI
GOOGLE_AI_API_KEY=your_google_ai_api_key

# LemonSqueezy
LEMON_SQUEEZY_SIGNING_SECRET=your_webhook_secret

# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:9002
```

### 4. Firebase Setup

1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication (Email/Password)
3. Create a Firestore database
4. Configure security rules (see `firestore.rules`)

### 5. Start Development Server

```bash
npm run dev
```

Visit [http://localhost:9002](http://localhost:9002) to see the application.

## 📚 Documentation

Comprehensive documentation is available in the `/docs` directory:

- **[User Guide](./docs/user-guide/README.md)** - Complete guide for end users
- **[Development Guide](./docs/development/)** - Developer documentation
- **[API Documentation](./docs/development/api.md)** - API reference
- **[Security Documentation](./docs/development/security.md)** - Security measures
- **[Error Handling](./docs/development/error-handling.md)** - Error boundary system
- **[Deployment Guide](./docs/deployment/README.md)** - Production deployment

## 🧪 Testing

```bash
# Run type checking
npm run typecheck

# Run linting
npm run lint

# Build for production
npm run build
```

## 🚀 Deployment

### Firebase Hosting (Recommended)

```bash
# Build the application
npm run build

# Deploy to Firebase
firebase deploy --only hosting
```

### Vercel

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

See the [Deployment Guide](./docs/deployment/README.md) for detailed instructions.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/contributing/README.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `npm run typecheck && npm run lint`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Browse the [documentation](./docs/) for detailed information
- **Issues**: Report bugs and feature requests on [GitHub Issues](https://github.com/your-username/metapdf/issues)
- **Email**: Contact <NAME_EMAIL>
- **Community**: Join our community discussions

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework for production
- [Firebase](https://firebase.google.com/) - Backend-as-a-Service platform
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Radix UI](https://www.radix-ui.com/) - Low-level UI primitives
- [PDF-lib](https://pdf-lib.js.org/) - PDF manipulation library
- [Lucide React](https://lucide.dev/) - Beautiful & consistent icons

## 📊 Project Status

- **Version**: 0.1.0
- **Status**: Active Development
- **Last Updated**: December 2024

---

**Built with ❤️ by the MetaPDF Team**
