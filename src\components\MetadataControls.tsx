
"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { EditIcon, SearchCheckIcon, FileTextIcon, SaveIcon, Trash2Icon, LayoutTemplateIcon, FileSignatureIcon, Loader2, GemIcon, ChevronsUpDown, ChevronsDownUp } from 'lucide-react'; // Added GemIcon
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext'; // Added
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"; // Added Tooltip components

interface MetadataControlsProps {
  onBulkEditClick: () => void;
  onApplyTemplateClick: () => void;
  onRenameClick: () => void;
  onExtractClick: () => void;
  onSaveAllClick: () => void;
  onClearAllClick: () => void;
  isSeoView: boolean;
  onToggleSeoView: (checked: boolean) => void;
  hasFiles: boolean;
  hasSelection: boolean;
  isLoadingTemplates?: boolean;
  isLoadingSubscription?: boolean; // Added prop
  onExpandAllFiles: () => void;
  onCollapseAllFiles: () => void;
  allFilesExpanded: boolean;
}

const MetadataControls: React.FC<MetadataControlsProps> = ({
  onBulkEditClick,
  onApplyTemplateClick,
  onRenameClick,
  onExtractClick,
  onSaveAllClick,
  onClearAllClick,
  isSeoView,
  onToggleSeoView,
  hasFiles,
  hasSelection,
  isLoadingTemplates,
  isLoadingSubscription, // Destructure prop
  onExpandAllFiles,
  onCollapseAllFiles,
  allFilesExpanded,
}) => {
  const { user } = useAuth();
  const { subscription } = useSubscription(); // Get subscription status

  if (!hasFiles) return null;

  const isFreePlan = subscription?.planId === 'free';

  const isApplyTemplateDisabled = !hasSelection || !user || isLoadingTemplates || isFreePlan || isLoadingSubscription;
  let applyTemplateButtonText = "Apply Template";
  if (user && isLoadingTemplates) {
    applyTemplateButtonText = "Loading Templates...";
  } else if (!user && !isLoadingSubscription) {
    applyTemplateButtonText = "Login for Templates";
  } else if (isFreePlan && !isLoadingSubscription) {
    applyTemplateButtonText = "Apply Template (Pro)";
  }

  const isExtractDisabled = isFreePlan || isLoadingSubscription;
  const extractButtonText = (isFreePlan && !isLoadingSubscription) ? "Extract All (Pro)" : "Extract All Metadata";


  return (
    <TooltipProvider delayDuration={300}>
      <div className="mb-6 p-4 bg-card rounded-lg shadow border border-border flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex flex-col sm:flex-row items-center gap-4 self-center md:self-auto">
          <div className="flex items-center space-x-2">
            <Switch
              id="seo-view-toggle"
              checked={isSeoView}
              onCheckedChange={onToggleSeoView}
              aria-label="Toggle SEO View"
            />
            <Label htmlFor="seo-view-toggle" className="flex items-center cursor-pointer">
              <SearchCheckIcon className="mr-2 h-5 w-5" /> SEO View
            </Label>
          </div>
           <div className="flex items-center space-x-2">
            {allFilesExpanded ? (
              <Button onClick={onCollapseAllFiles} variant="outline" size="sm" className="px-2.5">
 <ChevronsDownUp className="mr-1.5 h-4 w-4 text-muted-foreground" /> Collapse All
              </Button>
            ) : (
              <Button onClick={onExpandAllFiles} variant="outline" size="sm" className="px-2.5">
 <ChevronsUpDown className="mr-1.5 h-4 w-4 text-muted-foreground" /> Expand All
              </Button>
            )}
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:flex md:flex-row flex-wrap items-center justify-center md:justify-end gap-2 w-full md:w-auto">
          <Button onClick={onBulkEditClick} disabled={!hasSelection || isLoadingSubscription} variant="outline" className="w-full md:w-auto">
            <EditIcon className="mr-2 h-4 w-4" /> Bulk Edit Selected
          </Button>
 {/* Grouped Action Buttons */}
          <Tooltip>
            <TooltipTrigger asChild>
              {/* Span needed for tooltip to work on disabled button */}
              <span tabIndex={isApplyTemplateDisabled ? 0 : -1}> 
                <Button
                  onClick={onApplyTemplateClick}
                  disabled={isApplyTemplateDisabled}
                  variant="outline"
                  className="w-full md:w-auto"
                >
                  {isLoadingTemplates || (isLoadingSubscription && user) ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    isFreePlan && !isLoadingSubscription ? <GemIcon className="mr-2 h-4 w-4 text-accent" /> : <LayoutTemplateIcon className="mr-2 h-4 w-4" />
                  )}
                  {applyTemplateButtonText}
                </Button>
              </span>
            </TooltipTrigger>
            {isApplyTemplateDisabled && !isLoadingSubscription && (
              <TooltipContent>
                <p>{!user ? "Login to use templates." : (isFreePlan ? "Upgrade to Pro to use templates." : "Select files to apply a template.")}</p>
              </TooltipContent>
            )}
          </Tooltip>

          <Button onClick={onRenameClick} disabled={!hasSelection || isLoadingSubscription} variant="outline" className="w-full md:w-auto">
            <FileSignatureIcon className="mr-2 h-4 w-4" /> Rename Selected
          </Button>

          <Tooltip>
            <TooltipTrigger asChild>
              <span tabIndex={isExtractDisabled ? 0 : -1}>
                <Button 
                  onClick={onExtractClick} 
                  variant="outline" 
                  className="w-full md:w-auto"
                  disabled={isExtractDisabled}
                >
                   {isLoadingSubscription ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : (isFreePlan ? <GemIcon className="mr-2 h-4 w-4 text-accent" /> : <FileTextIcon className="mr-2 h-4 w-4" />)}
                  {extractButtonText}
                </Button>
              </span>
            </TooltipTrigger>
            {isExtractDisabled && !isLoadingSubscription && (
              <TooltipContent>
                <p>Upgrade to Pro to extract all metadata.</p>
              </TooltipContent>
            )}
          </Tooltip>

          <Button onClick={onSaveAllClick} variant="default" className="w-full md:w-auto" disabled={isLoadingSubscription}>
            {isLoadingSubscription ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <SaveIcon className="mr-2 h-4 w-4" />}
             Save All Changes
          </Button>
          <Button onClick={onClearAllClick} variant="destructive" className="w-full md:w-auto" disabled={isLoadingSubscription}>
            {isLoadingSubscription ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2Icon className="mr-2 h-4 w-4" />}
             Clear All Files
          </Button>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default MetadataControls;
