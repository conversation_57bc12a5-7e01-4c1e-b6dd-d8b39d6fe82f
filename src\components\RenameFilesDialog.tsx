
"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import type { PdfFile } from '@/types';

interface RenameFilesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyRename: (pattern: string) => void;
  numFilesSelected: number;
  firstSelectedFile: PdfFile | undefined;
}

const placeholders = [
  { value: "[Title]", description: "The document's title metadata." },
  { value: "[Author]", description: "The document's author metadata." },
  { value: "[Subject]", description: "The document's subject/description metadata." },
  { value: "[OriginalFileName]", description: "The original filename at upload (without extension)." },
];

// Basic filename sanitization: removes common invalid characters
const sanitizeFilenamePart = (part: string): string => {
  return part.replace(/[\\/:*?"<>|]/g, '');
};

const generatePreviewName = (pattern: string, file?: PdfFile): string => {
  if (!file) return "No file selected for preview";

  let newName = pattern;
  newName = newName.replace(/\[Title\]/g, sanitizeFilenamePart(file.metadata.title || ''));
  newName = newName.replace(/\[Author\]/g, sanitizeFilenamePart(file.metadata.author || ''));
  newName = newName.replace(/\[Subject\]/g, sanitizeFilenamePart(file.metadata.subject || ''));
  
  const originalNameWithoutExtension = file.originalNameOnUpload.substring(0, file.originalNameOnUpload.lastIndexOf('.')) || file.originalNameOnUpload;
  newName = newName.replace(/\[OriginalFileName\]/g, sanitizeFilenamePart(originalNameWithoutExtension));

  const extension = file.originalNameOnUpload.substring(file.originalNameOnUpload.lastIndexOf('.'));
  return `${newName || 'untitled'}${extension}`;
};


const RenameFilesDialog: React.FC<RenameFilesDialogProps> = ({
  isOpen,
  onClose,
  onApplyRename,
  numFilesSelected,
  firstSelectedFile,
}) => {
  const [pattern, setPattern] = useState<string>("[Author] - [Title]");
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen) {
      // Reset pattern if needed or keep last used
      // setPattern("[Author] - [Title]");
    }
  }, [isOpen]);

  const handleApply = () => {
    if (!pattern.trim()) {
      toast({ title: "Pattern Required", description: "Please enter a renaming pattern.", variant: "destructive" });
      return;
    }
    if (!placeholders.some(p => pattern.includes(p.value))) {
        toast({ title: "Invalid Pattern", description: "Pattern must include at least one placeholder (e.g., [Title]).", variant: "destructive" });
        return;
    }
    onApplyRename(pattern);
    onClose();
  };

  const previewName = useMemo(() => generatePreviewName(pattern, firstSelectedFile), [pattern, firstSelectedFile]);

  if (numFilesSelected === 0 && isOpen) {
    onClose(); // Should not happen if button is disabled
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-card text-card-foreground">
        <DialogHeader>
          <DialogTitle>Rename Selected Files</DialogTitle>
          <DialogDescription>
            Define a pattern to rename the {numFilesSelected} selected file(s).
            The original file extension will be preserved.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-4">
          <div>
            <Label htmlFor="rename-pattern">Renaming Pattern</Label>
            <Input
              id="rename-pattern"
              value={pattern}
              onChange={(e) => setPattern(e.target.value)}
              placeholder="e.g., [Author] - [Title]"
              className="mt-1"
            />
          </div>
          <div>
            <Label className="text-sm font-medium">Available Placeholders:</Label>
            <ul className="mt-1 list-disc list-inside text-xs text-muted-foreground space-y-0.5">
              {placeholders.map(p => <li key={p.value}><strong>{p.value}</strong>: {p.description}</li>)}
            </ul>
          </div>
          {firstSelectedFile && (
            <div>
              <Label className="text-sm font-medium">Preview (for {firstSelectedFile.originalNameOnUpload}):</Label>
              <p className="mt-1 text-xs p-2 bg-muted rounded break-all">{previewName}</p>
            </div>
          )}
        </div>

        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <Button onClick={handleApply} disabled={!pattern.trim() || !placeholders.some(p => pattern.includes(p.value))}>Apply Rename</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RenameFilesDialog;
