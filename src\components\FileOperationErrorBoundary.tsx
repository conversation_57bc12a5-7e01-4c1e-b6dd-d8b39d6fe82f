'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { FileX, RefreshCw, Upload } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Props {
  children: ReactNode;
  onRetry?: () => void;
  onClearFiles?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorId: string;
}

/**
 * Specialized Error Boundary for File Operations
 * 
 * Handles errors specifically related to file upload, processing, and metadata operations.
 * Provides contextual recovery options for file-related errors.
 */
export class FileOperationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `file_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[FileOperationErrorBoundary] File operation error:', error);
    console.error('[FileOperationErrorBoundary] Error info:', errorInfo);

    // Log specific file operation errors
    this.logFileError(error, errorInfo);
  }

  private logFileError = (error: Error, errorInfo: ErrorInfo) => {
    const errorData = {
      type: 'file_operation_error',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      context: 'file_operations',
    };

    console.error('[FileOperationErrorBoundary] File error logged:', errorData);
    
    // TODO: Send to error tracking service with file operation context
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorId: '',
    });

    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  private handleClearFiles = () => {
    this.setState({
      hasError: false,
      error: null,
      errorId: '',
    });

    if (this.props.onClearFiles) {
      this.props.onClearFiles();
    }
  };

  private getErrorMessage = (error: Error): string => {
    const message = error.message.toLowerCase();
    
    if (message.includes('file') && message.includes('size')) {
      return 'The selected file is too large. Please choose a smaller PDF file.';
    }
    
    if (message.includes('pdf') || message.includes('format')) {
      return 'The selected file appears to be corrupted or is not a valid PDF. Please try a different file.';
    }
    
    if (message.includes('memory') || message.includes('heap')) {
      return 'The file is too complex to process. Try with a smaller or simpler PDF file.';
    }
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'Network error occurred while processing the file. Please check your connection and try again.';
    }
    
    if (message.includes('permission') || message.includes('access')) {
      return 'Unable to access the file. The PDF might be password-protected or have restricted permissions.';
    }

    return 'An error occurred while processing your PDF file. This might be due to file corruption, unsupported PDF features, or temporary system issues.';
  };

  render() {
    if (this.state.hasError) {
      const { error, errorId } = this.state;
      const errorMessage = error ? this.getErrorMessage(error) : 'An unknown error occurred.';

      return (
        <div className="flex min-h-[300px] items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 text-destructive">
                <FileX className="h-full w-full" />
              </div>
              <CardTitle>File Processing Error</CardTitle>
              <CardDescription>
                There was a problem processing your PDF file.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <FileX className="h-4 w-4" />
                <AlertDescription>
                  {errorMessage}
                </AlertDescription>
              </Alert>

              <Alert>
                <AlertDescription>
                  Error ID: <code className="text-sm font-mono">{errorId}</code>
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Troubleshooting Tips:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Ensure the file is a valid PDF document</li>
                  <li>• Try with a smaller file (under 100MB for Free, 200MB for Pro)</li>
                  <li>• Check if the PDF is password-protected</li>
                  <li>• Verify your internet connection is stable</li>
                  <li>• Large files may take longer to process</li>
                </ul>
              </div>

              <div className="flex flex-col gap-2 sm:flex-row">
                <Button onClick={this.handleRetry} className="flex-1">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
                <Button onClick={this.handleClearFiles} variant="outline" className="flex-1">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload New File
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default FileOperationErrorBoundary;
