

export interface Metadata {
  title: string;
  author: string;
  subject: string;
  keywords: string; // Comma-separated string for input simplicity
  producer: string;
  creatorTool: string;
}

export interface PdfFile {
  id: string;
  name: string;
  originalNameOnUpload: string;
  metadata: Metadata;
  documentContentPreview: string;
  originalFileBuffer?: ArrayBuffer;
  isLoadingKeywords?: boolean;
  suggestedKeywords?: string[];
  isLoadingSubject?: boolean;
}

export interface MetadataTemplate {
  id: string; // Firestore document ID
  userId: string; // Firebase Auth UID of the owner
  name: string;
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  producer?: string;
  creatorTool?: string;
  createdAt?: Timestamp; // Firestore Timestamp
  updatedAt?: Timestamp; // Firestore Timestamp
}

export interface CreateTemplateData {
  name: string;
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  producer?: string;
  creatorTool?: string;
}

// --- Subscription Types ---
import type { Timestamp } from 'firebase/firestore';

export interface PlanLimits {
  maxFiles: number;
  keywordSuggestionsPerDay: number;
  subjectSuggestionsPerDay: number;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  priceMonthly?: number;
  priceYearly?: number;
  features: string[];
  limits: PlanLimits;
  stripePriceId?: string;
  lemonSqueezyCheckoutUrl?: string;
  lemonSqueezyVariantId?: string;
  hasAds?: boolean; // New field for ads
}

export interface AIUsage {
  count: number;
  lastResetDate: string; // YYYY-MM-DD format for simple daily reset
}

export type AddonType = 'keywordSuggestions' | 'subjectSuggestions';

export interface AddonPack {
  id: string;
  name: string;
  type: AddonType;
  quantity: number;
  price: number; // Price in GBP
  description: string;
  stripePriceId?: string; // Placeholder for Stripe
  lemonSqueezyCheckoutUrl?: string;
  lemonSqueezyVariantId?: string;
}

export interface UserSubscriptionData {
  planId: string;
  status: 'active' | 'inactive' | 'cancelled' | 'past_due' | 'trialing';
  currentPeriodEnd?: Timestamp | null;
  cancelAtPeriodEnd?: boolean;
  dailyUsage: {
    keywordSuggestions: AIUsage;
    subjectSuggestions: AIUsage;
  };
  addons: { // New field for purchased addons
    keywordSuggestions: number;
    subjectSuggestions: number;
  };
}

