
// src/ai/flows/suggest-keywords.ts
'use server';
/**
 * @fileOverview A flow that suggests keywords for SEO optimization based on document content.
 *
 * - suggestKeywords - A function that takes document content and returns keyword suggestions.
 * - SuggestKeywordsInput - The input type for the suggestKeywords function.
 * - SuggestKeywordsOutput - The return type for the suggestKeywordsOutput function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SuggestKeywordsInputSchema = z.object({
  documentContent: z
    .string()
    .describe('The content of the PDF document to analyze for keyword suggestions.'),
});
export type SuggestKeywordsInput = z.infer<typeof SuggestKeywordsInputSchema>;

const SuggestKeywordsOutputSchema = z.object({
  keywords: z
    .array(z.string())
    .describe('An array of keyword suggestions for the PDF document.'),
});
export type SuggestKeywordsOutput = z.infer<typeof SuggestKeywordsOutputSchema>;

export async function suggestKeywords(input: SuggestKeywordsInput): Promise<SuggestKeywordsOutput> {
  return suggestKeywordsFlow(input);
}

const suggestKeywordsPrompt = ai.definePrompt({
  name: 'suggestKeywordsPrompt',
  input: {schema: SuggestKeywordsInputSchema},
  output: {schema: SuggestKeywordsOutputSchema},
  prompt: `You are an expert SEO strategist. Your goal is to help optimize PDF document metadata for search engines.
Analyze the following document content and provide a list of 5 to 10 high-impact keywords.
These keywords should be:
- Highly relevant to the core topics and themes of the document.
- Optimized for search engine visibility.
- A mix of primary (short-tail) keywords and relevant long-tail keywords.
- Useful for populating the 'keywords' metadata field of a PDF.

Return your answer as a JSON object with a single key "keywords" whose value is an array of 5 to 10 high-impact keyword strings.

Document Content: {{{documentContent}}}`,
});

const suggestKeywordsFlow = ai.defineFlow(
  {
    name: 'suggestKeywordsFlow',
    inputSchema: SuggestKeywordsInputSchema,
    outputSchema: SuggestKeywordsOutputSchema,
  },
  async input => {
    const {output} = await suggestKeywordsPrompt(input);
    if (!output) {
      throw new Error('AI failed to generate keyword suggestions or the output was invalid.');
    }
    return output;
  }
);

