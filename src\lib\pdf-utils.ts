/**
 * PDF Utilities with Dynamic Imports
 * 
 * This module provides PDF processing utilities that work safely in both
 * server-side and client-side environments by using dynamic imports.
 */

// Cache for dynamically imported modules
let pdfLibCache: any = null;
let pdfjsCache: any = null;

/**
 * Initialize PDF-lib with dynamic import
 * Avoids SSR issues and canvas dependencies
 */
export async function initializePDFLib() {
  if (typeof window !== 'undefined' && !pdfLibCache) {
    try {
      pdfLibCache = await import('pdf-lib');
      return pdfLibCache;
    } catch (error) {
      console.error('Failed to load PDF-lib:', error);
      throw new Error('PDF-lib failed to load');
    }
  }
  return pdfLibCache;
}

/**
 * Initialize PDF.js with dynamic import
 * Configures worker and avoids Node.js dependencies
 */
export async function initializePDFJS() {
  if (typeof window !== 'undefined' && !pdfjsCache) {
    try {
      pdfjsCache = await import('pdfjs-dist');
      
      // Set worker source for pdf.js
      pdfjsCache.GlobalWorkerOptions.workerSrc = 
        `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsCache.version}/pdf.worker.min.js`;
      
      return pdfjsCache;
    } catch (error) {
      console.error('Failed to load PDF.js:', error);
      throw new Error('PDF.js failed to load');
    }
  }
  return pdfjsCache;
}

/**
 * Extract text content from PDF using PDF.js
 */
export async function extractTextFromPDF(arrayBuffer: ArrayBuffer): Promise<string> {
  const pdfjsLib = await initializePDFJS();
  
  try {
    const pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    let extractedText = '';
    
    for (let i = 1; i <= pdfDoc.numPages; i++) {
      const page = await pdfDoc.getPage(i);
      const textContent = await page.getTextContent();
      extractedText += textContent.items.map((item: any) => item.str).join(' ') + '\n';
    }
    
    return extractedText.trim();
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    throw new Error('Failed to extract text from PDF');
  }
}

/**
 * Extract metadata from PDF using PDF.js
 */
export async function extractMetadataFromPDF(arrayBuffer: ArrayBuffer): Promise<any> {
  const pdfjsLib = await initializePDFJS();
  
  try {
    const pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    const { info } = await pdfDoc.getMetadata();
    return info;
  } catch (error) {
    console.error('Error extracting metadata from PDF:', error);
    return null;
  }
}

/**
 * Create a new PDF with updated metadata using PDF-lib
 */
export async function updatePDFMetadata(
  originalBuffer: ArrayBuffer,
  metadata: {
    title?: string;
    author?: string;
    subject?: string;
    keywords?: string;
    producer?: string;
    creator?: string;
  }
): Promise<Uint8Array> {
  const pdfLib = await initializePDFLib();
  
  try {
    const pdfDoc = await pdfLib.PDFDocument.load(originalBuffer);
    
    // Update metadata
    if (metadata.title !== undefined) pdfDoc.setTitle(metadata.title);
    if (metadata.author !== undefined) pdfDoc.setAuthor(metadata.author);
    if (metadata.subject !== undefined) pdfDoc.setSubject(metadata.subject);
    if (metadata.keywords !== undefined) pdfDoc.setKeywords(metadata.keywords);
    if (metadata.producer !== undefined) pdfDoc.setProducer(metadata.producer);
    if (metadata.creator !== undefined) pdfDoc.setCreator(metadata.creator);
    
    // Set modification date
    pdfDoc.setModificationDate(new Date());
    
    // Save the PDF
    return await pdfDoc.save();
  } catch (error) {
    console.error('Error updating PDF metadata:', error);
    throw new Error('Failed to update PDF metadata');
  }
}

/**
 * Validate if a file is a valid PDF
 */
export async function validatePDF(arrayBuffer: ArrayBuffer): Promise<boolean> {
  try {
    const pdfjsLib = await initializePDFJS();
    const pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    return pdfDoc.numPages > 0;
  } catch (error) {
    console.error('PDF validation failed:', error);
    return false;
  }
}

/**
 * Get PDF information (page count, etc.)
 */
export async function getPDFInfo(arrayBuffer: ArrayBuffer): Promise<{
  numPages: number;
  fileSize: number;
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
}> {
  const pdfjsLib = await initializePDFJS();
  
  try {
    const pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    const { info } = await pdfDoc.getMetadata();
    
    return {
      numPages: pdfDoc.numPages,
      fileSize: arrayBuffer.byteLength,
      title: (info as any)?.Title || '',
      author: (info as any)?.Author || '',
      subject: (info as any)?.Subject || '',
      keywords: (info as any)?.Keywords || '',
    };
  } catch (error) {
    console.error('Error getting PDF info:', error);
    throw new Error('Failed to get PDF information');
  }
}

/**
 * Check if PDF processing is available in current environment
 */
export function isPDFProcessingAvailable(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Error types for PDF operations
 */
export class PDFProcessingError extends Error {
  constructor(message: string, public readonly operation: string) {
    super(message);
    this.name = 'PDFProcessingError';
  }
}

export class PDFValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PDFValidationError';
  }
}
