
// src/services/userService.ts
'use client';
import { firestore } from '@/lib/firebase';
import { doc, setDoc, getDoc, serverTimestamp, updateDoc } from 'firebase/firestore';
import type { UserSubscriptionData } from '@/types';
import { DEFAULT_PLAN_ID } from '@/config/plans';

function getTodayDateString(): string {
  return new Date().toISOString().split('T')[0]; // YYYY-MM-DD
}

// Ensures a user document has a default subscription structure and resets daily usage if needed.
export async function ensureUserSubscription(userId: string): Promise<UserSubscriptionData> {
  const userRef = doc(firestore, 'users', userId);
  const userSnap = await getDoc(userRef);
  const today = getTodayDateString();

  const defaultSubscriptionData: UserSubscriptionData = {
    planId: DEFAULT_PLAN_ID,
    status: 'active',
    dailyUsage: {
      keywordSuggestions: { count: 0, lastResetDate: today },
      subjectSuggestions: { count: 0, lastResetDate: today },
    },
    addons: { // Initialize addons
      keywordSuggestions: 0,
      subjectSuggestions: 0,
    },
  };

  if (userSnap.exists()) {
    const userData = userSnap.data();
    let currentSubscription = userData.subscription as UserSubscriptionData | undefined;
    let needsUpdate = false;

    if (!currentSubscription) {
      currentSubscription = defaultSubscriptionData;
      needsUpdate = true;
    } else {
      // Ensure dailyUsage structure and fields exist
      if (!currentSubscription.dailyUsage) {
        currentSubscription.dailyUsage = defaultSubscriptionData.dailyUsage;
        needsUpdate = true;
      } else {
        if (!currentSubscription.dailyUsage.keywordSuggestions || typeof currentSubscription.dailyUsage.keywordSuggestions.count !== 'number' || !currentSubscription.dailyUsage.keywordSuggestions.lastResetDate) {
          currentSubscription.dailyUsage.keywordSuggestions = defaultSubscriptionData.dailyUsage.keywordSuggestions;
          needsUpdate = true;
        }
        if (!currentSubscription.dailyUsage.subjectSuggestions || typeof currentSubscription.dailyUsage.subjectSuggestions.count !== 'number' || !currentSubscription.dailyUsage.subjectSuggestions.lastResetDate) {
          currentSubscription.dailyUsage.subjectSuggestions = defaultSubscriptionData.dailyUsage.subjectSuggestions;
          needsUpdate = true;
        }
      }
      // Ensure addons structure exists
      if (!currentSubscription.addons) {
        currentSubscription.addons = defaultSubscriptionData.addons;
        needsUpdate = true;
      } else {
        if (typeof currentSubscription.addons.keywordSuggestions !== 'number') {
            currentSubscription.addons.keywordSuggestions = 0;
            needsUpdate = true;
        }
        if (typeof currentSubscription.addons.subjectSuggestions !== 'number') {
            currentSubscription.addons.subjectSuggestions = 0;
            needsUpdate = true;
        }
      }


      // Reset daily counts if lastResetDate is not today
      if (currentSubscription.dailyUsage.keywordSuggestions.lastResetDate !== today) {
        currentSubscription.dailyUsage.keywordSuggestions = { count: 0, lastResetDate: today };
        needsUpdate = true;
      }
      if (currentSubscription.dailyUsage.subjectSuggestions.lastResetDate !== today) {
        currentSubscription.dailyUsage.subjectSuggestions = { count: 0, lastResetDate: today };
        needsUpdate = true;
      }
    }

    if (needsUpdate) {
      await updateDoc(userRef, { subscription: currentSubscription });
    }
    return currentSubscription;
  } else {
    // User document doesn't exist - this case should be rare if initializeUserDocument runs on login/signup.
    // initializeUserDocument will handle creating the full doc.
    console.error(`User document for ${userId} not found in ensureUserSubscription. This should have been created by initializeUserDocument.`);
    return defaultSubscriptionData;
  }
}


export async function initializeUserDocument(userId: string, email: string | null): Promise<void> {
  const userRef = doc(firestore, 'users', userId);
  const userSnap = await getDoc(userRef);
  const today = getTodayDateString();

  const defaultSubscription: UserSubscriptionData = {
    planId: DEFAULT_PLAN_ID,
    status: 'active',
    dailyUsage: {
      keywordSuggestions: { count: 0, lastResetDate: today },
      subjectSuggestions: { count: 0, lastResetDate: today },
    },
    addons: { // Initialize addons
      keywordSuggestions: 0,
      subjectSuggestions: 0,
    },
  };

  if (!userSnap.exists()) {
    await setDoc(userRef, {
      email: email,
      createdAt: serverTimestamp(),
      subscription: defaultSubscription,
    });
  } else {
    // User exists, ensure subscription part is valid and daily usage is reset if needed
    const currentData = userSnap.data();
    let subscriptionData = currentData.subscription as UserSubscriptionData | undefined;
    let requiresUpdate = false;

    if (!subscriptionData || 
        !subscriptionData.dailyUsage || 
        !subscriptionData.dailyUsage.keywordSuggestions || 
        !subscriptionData.dailyUsage.subjectSuggestions ||
        !subscriptionData.addons || // Check for addons field
        typeof subscriptionData.addons.keywordSuggestions !== 'number' ||
        typeof subscriptionData.addons.subjectSuggestions !== 'number'
       ) {
      // If any crucial part is missing, reset/initialize the subscription structure
      subscriptionData = {
          ...defaultSubscription, // Start with defaults
          planId: subscriptionData?.planId || DEFAULT_PLAN_ID, // Preserve planId if it exists
          status: subscriptionData?.status || 'active', // Preserve status
      };
      requiresUpdate = true;
    } else {
      if (subscriptionData.dailyUsage.keywordSuggestions.lastResetDate !== today) {
        subscriptionData.dailyUsage.keywordSuggestions = { count: 0, lastResetDate: today };
        requiresUpdate = true;
      }
      if (subscriptionData.dailyUsage.subjectSuggestions.lastResetDate !== today) {
        subscriptionData.dailyUsage.subjectSuggestions = { count: 0, lastResetDate: today };
        requiresUpdate = true;
      }
    }
    if (requiresUpdate) {
        await updateDoc(userRef, { subscription: subscriptionData });
    }
  }
}
