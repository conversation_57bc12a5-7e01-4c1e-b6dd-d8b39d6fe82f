# API Documentation

MetaPDF provides several API endpoints for webhook handling, user management, and external integrations. This document covers all available APIs, their usage, authentication, and security considerations.

## API Overview

### Base URL
- **Production**: `https://www.metapdf.app/api`
- **Development**: `http://localhost:9002/api`

### Authentication
Most API endpoints use Firebase Authentication tokens. Include the token in the Authorization header:

```
Authorization: Bearer <firebase_id_token>
```

### Response Format
All API responses follow a consistent JSON format:

```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2024-12-19T10:30:00Z"
}
```

Error responses:
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE",
  "timestamp": "2024-12-19T10:30:00Z"
}
```

## Webhook Endpoints

### LemonSqueezy Webhook

**Endpoint**: `POST /api/lemonsqueezy-webhook`

Handles subscription events from LemonSqueezy payment processor.

#### Headers
```
Content-Type: application/json
X-Signature: <hmac_signature>
```

#### Request Body
```json
{
  "meta": {
    "event_name": "subscription_created",
    "custom_data": {
      "user_id": "firebase_user_id"
    }
  },
  "data": {
    "id": "subscription_id",
    "attributes": {
      "status": "active",
      "product_id": 123,
      "variant_id": 456
    }
  }
}
```

#### Supported Events

##### subscription_created
Triggered when a new subscription is created.

**Response**: `200 OK`
```json
{
  "message": "Subscription created successfully"
}
```

##### subscription_updated
Triggered when subscription details change.

**Response**: `200 OK`
```json
{
  "message": "Subscription updated successfully"
}
```

##### subscription_cancelled
Triggered when a subscription is cancelled.

**Response**: `200 OK`
```json
{
  "message": "Subscription cancelled successfully"
}
```

##### order_created
Triggered when a one-time purchase is made.

**Response**: `200 OK`
```json
{
  "message": "Order processed successfully"
}
```

#### Security

- **Signature Verification**: All webhooks must include valid HMAC signature
- **IP Whitelisting**: Only LemonSqueezy IPs accepted (in production)
- **Rate Limiting**: Maximum 100 requests per minute per IP

#### Error Responses

**400 Bad Request**
```json
{
  "error": "Invalid JSON payload"
}
```

**401 Unauthorized**
```json
{
  "error": "Invalid signature"
}
```

**500 Internal Server Error**
```json
{
  "error": "Internal server error while processing webhook"
}
```

## User Management APIs

### Get User Profile

**Endpoint**: `GET /api/user/profile`

Retrieves the current user's profile information.

#### Headers
```
Authorization: Bearer <firebase_id_token>
```

#### Response
```json
{
  "success": true,
  "data": {
    "uid": "firebase_user_id",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "subscription": {
      "plan": "pro",
      "status": "active",
      "expiresAt": "2024-12-31T23:59:59Z"
    },
    "usage": {
      "aiOperations": 150,
      "filesProcessed": 45
    }
  }
}
```

### Update User Profile

**Endpoint**: `PUT /api/user/profile`

Updates user profile information.

#### Headers
```
Authorization: Bearer <firebase_id_token>
Content-Type: application/json
```

#### Request Body
```json
{
  "displayName": "John Smith",
  "preferences": {
    "theme": "dark",
    "notifications": true
  }
}
```

#### Response
```json
{
  "success": true,
  "message": "Profile updated successfully"
}
```

## File Processing APIs

### Upload File

**Endpoint**: `POST /api/files/upload`

Uploads and processes a PDF file.

#### Headers
```
Authorization: Bearer <firebase_id_token>
Content-Type: multipart/form-data
```

#### Request Body
```
file: <pdf_file>
```

#### Response
```json
{
  "success": true,
  "data": {
    "fileId": "unique_file_id",
    "filename": "document.pdf",
    "size": 1024000,
    "metadata": {
      "title": "Document Title",
      "author": "Author Name",
      "subject": "Document Subject"
    }
  }
}
```

### Get File Metadata

**Endpoint**: `GET /api/files/{fileId}/metadata`

Retrieves metadata for a specific file.

#### Headers
```
Authorization: Bearer <firebase_id_token>
```

#### Response
```json
{
  "success": true,
  "data": {
    "fileId": "unique_file_id",
    "metadata": {
      "title": "Document Title",
      "author": "Author Name",
      "subject": "Document Subject",
      "keywords": "keyword1, keyword2",
      "creator": "MetaPDF",
      "producer": "PDF Library",
      "creationDate": "2024-01-01T00:00:00Z",
      "modificationDate": "2024-01-02T00:00:00Z"
    }
  }
}
```

### Update File Metadata

**Endpoint**: `PUT /api/files/{fileId}/metadata`

Updates metadata for a specific file.

#### Headers
```
Authorization: Bearer <firebase_id_token>
Content-Type: application/json
```

#### Request Body
```json
{
  "title": "Updated Title",
  "author": "Updated Author",
  "subject": "Updated Subject",
  "keywords": "updated, keywords"
}
```

#### Response
```json
{
  "success": true,
  "message": "Metadata updated successfully"
}
```

## AI Services APIs

### Generate Keywords

**Endpoint**: `POST /api/ai/keywords`

Generates keyword suggestions using AI.

#### Headers
```
Authorization: Bearer <firebase_id_token>
Content-Type: application/json
```

#### Request Body
```json
{
  "content": "Document content for analysis",
  "maxKeywords": 10
}
```

#### Response
```json
{
  "success": true,
  "data": {
    "keywords": [
      "artificial intelligence",
      "machine learning",
      "data analysis"
    ],
    "confidence": 0.95,
    "usage": {
      "tokensUsed": 150,
      "remainingQuota": 850
    }
  }
}
```

### Generate Description

**Endpoint**: `POST /api/ai/description`

Generates description suggestions using AI.

#### Headers
```
Authorization: Bearer <firebase_id_token>
Content-Type: application/json
```

#### Request Body
```json
{
  "content": "Document content for analysis",
  "maxLength": 200
}
```

#### Response
```json
{
  "success": true,
  "data": {
    "description": "Generated description based on content analysis...",
    "confidence": 0.92,
    "usage": {
      "tokensUsed": 200,
      "remainingQuota": 800
    }
  }
}
```

## Analytics APIs

### Get Usage Statistics

**Endpoint**: `GET /api/analytics/usage`

Retrieves user usage statistics.

#### Headers
```
Authorization: Bearer <firebase_id_token>
```

#### Query Parameters
- `period`: `day|week|month|year` (default: `month`)
- `startDate`: ISO date string (optional)
- `endDate`: ISO date string (optional)

#### Response
```json
{
  "success": true,
  "data": {
    "period": "month",
    "filesProcessed": 45,
    "aiOperations": 150,
    "totalSize": 50000000,
    "breakdown": {
      "uploads": 45,
      "keywordSuggestions": 75,
      "descriptionSuggestions": 75
    }
  }
}
```

## Error Handling

### Error Codes

| Code | Description |
|------|-------------|
| `INVALID_TOKEN` | Firebase authentication token is invalid |
| `EXPIRED_TOKEN` | Authentication token has expired |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| `QUOTA_EXCEEDED` | User has exceeded their usage quota |
| `FILE_TOO_LARGE` | Uploaded file exceeds size limit |
| `INVALID_FILE_TYPE` | File type is not supported |
| `PROCESSING_ERROR` | Error occurred during file processing |
| `AI_SERVICE_ERROR` | AI service is unavailable |
| `RATE_LIMIT_EXCEEDED` | Too many requests in time window |

### Rate Limiting

All API endpoints are rate-limited to prevent abuse:

- **Authenticated requests**: 1000 requests per hour per user
- **File uploads**: 3 files (Free) / 25 files (Pro) per session
- **AI operations**: 5/day (Free) / 100/day (Pro) + add-on packs
- **Webhook endpoints**: 100 requests per minute per IP

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## SDK and Integration

### JavaScript SDK Example

```javascript
class MetaPDFAPI {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseURL = 'https://www.metapdf.app/api';
  }

  async uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.baseURL}/files/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: formData,
    });

    return response.json();
  }

  async generateKeywords(content) {
    const response = await fetch(`${this.baseURL}/ai/keywords`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ content }),
    });

    return response.json();
  }
}
```

### cURL Examples

#### Upload File
```bash
curl -X POST \
  https://www.metapdf.app/api/files/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.pdf"
```

#### Generate Keywords
```bash
curl -X POST \
  https://www.metapdf.app/api/ai/keywords \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"content": "Document content here"}'
```

## Testing

### Test Environment
- **Base URL**: `https://test.metapdf.app/api`
- **Test API Keys**: Available in developer dashboard
- **Mock Data**: Consistent test responses

### Webhook Testing
Use tools like ngrok for local webhook testing:

```bash
# Start local server
npm run dev

# Expose local server
ngrok http 9002

# Use ngrok URL for webhook configuration
https://abc123.ngrok.io/api/lemonsqueezy-webhook
```

---

This API documentation provides comprehensive coverage of all MetaPDF API endpoints, ensuring developers can integrate effectively with the platform.
