
import { firestore, auth } from '@/lib/firebase';
import type { MetadataTemplate, CreateTemplateData } from '@/types';
import { collection, addDoc, getDocs, getDoc, query, serverTimestamp, type Timestamp, doc, updateDoc, deleteDoc } from 'firebase/firestore';

export type UpdateTemplateData = Partial<CreateTemplateData>;


export async function saveTemplate(
  clientUserIdForPath: string,
  templateData: CreateTemplateData
): Promise<MetadataTemplate | null> {

  if (!clientUserIdForPath || typeof clientUserIdForPath !== 'string' || clientUserIdForPath.trim() === '') {
    console.error('[SvcSaveTemplate] Critical Error: Client-provided userId for path is invalid or empty. Aborting save. Provided userId was:', `"${clientUserIdForPath}"`);
    return null;
  }
  
  if (!auth || !auth.currentUser) {
    console.error('[SvcSaveTemplate] Critical Error: Firebase auth object or currentUser is not available. Aborting save.');
    return null;
  }

  if (auth.currentUser.uid !== clientUserIdForPath) {
    console.error(`[SvcSaveTemplate] Mismatch: clientUserIdForPath ("${clientUserIdForPath}") does not match auth.currentUser.uid ("${auth.currentUser.uid}"). Aborting.`);
    return null;
  }


  const firestorePath = `users/${clientUserIdForPath}/templates`;

  const dataToSave = {
    userId: auth.currentUser.uid, 
    name: templateData.name.trim(),
    title: templateData.title?.trim() ?? "",
    author: templateData.author?.trim() ?? "",
    subject: templateData.subject?.trim() ?? "",
    keywords: templateData.keywords?.trim() ?? "",
    producer: templateData.producer?.trim() ?? "",
    creatorTool: templateData.creatorTool?.trim() ?? "",
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
  };

  try {
    const docRef = await addDoc(collection(firestore, firestorePath), dataToSave);
    
    // Constructing the full template object to return, simulating the serverTimestamp resolution locally for immediate use.
    // Firestore Timestamps will be properly handled when fetched.
    const savedTemplate: MetadataTemplate = {
      id: docRef.id,
      userId: dataToSave.userId,
      name: dataToSave.name,
      title: dataToSave.title,
      author: dataToSave.author,
      subject: dataToSave.subject,
      keywords: dataToSave.keywords,
      producer: dataToSave.producer,
      creatorTool: dataToSave.creatorTool,
      // createdAt and updatedAt will be Timestamps when fetched
    };
    return savedTemplate;

  } catch (error: any) {
    console.error(`[SvcSaveTemplate] Firestore 'addDoc' FAILED for path "${firestorePath}".`);
    console.error(`[SvcSaveTemplate] Error Code: ${error.code}`);
    console.error(`[SvcSaveTemplate] Error Message: ${error.message}`);
    console.error('[SvcSaveTemplate] Full Error Object:', error);
    return null;
  }
}

export async function getTemplates(clientUserIdForPath: string): Promise<MetadataTemplate[]> {
  if (!clientUserIdForPath || typeof clientUserIdForPath !== 'string' || clientUserIdForPath.trim() === '') {
    console.error('[SvcGetTemplates] Critical Error: Client-provided userId for path is invalid or empty. Aborting fetch. Provided userId was:', `"${clientUserIdForPath}"`);
    return [];
  }

  if (!auth || !auth.currentUser) {
    console.error('[SvcGetTemplates] Critical Error: Firebase auth object or currentUser is not available. Aborting fetch.');
    return [];
  }
  
  if (auth.currentUser.uid !== clientUserIdForPath) {
    console.error(`[SvcGetTemplates] Mismatch: clientUserIdForPath ("${clientUserIdForPath}") does not match auth.currentUser.uid ("${auth.currentUser.uid}"). Aborting.`);
    return [];
  }

  const firestorePath = `users/${clientUserIdForPath}/templates`;

  try {
    const q = query(collection(firestore, firestorePath));
    const querySnapshot = await getDocs(q);
    const templates: MetadataTemplate[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const docUserId = data.userId || auth.currentUser!.uid; // auth.currentUser checked above
      
      templates.push({
        id: doc.id,
        userId: docUserId,
        name: data.name || "Unnamed Template",
        title: data.title || "",
        author: data.author || "",
        subject: data.subject || "",
        keywords: data.keywords || "",
        producer: data.producer || "",
        creatorTool: data.creatorTool || "",
        createdAt: data.createdAt, 
        updatedAt: data.updatedAt, 
      });
    });
    return templates;
  } catch (error: any) {
    console.error(`[SvcGetTemplates] Firestore 'getDocs' FAILED for path "${firestorePath}".`);
    console.error(`[SvcGetTemplates] Error Code: ${error.code}`);
    console.error(`[SvcGetTemplates] Error Message: ${error.message}`);
    console.error('[SvcGetTemplates] Full Error Object:', error);
    return [];
  }
}

export async function updateTemplate(
  clientUserIdForPath: string,
  templateId: string,
  templateData: UpdateTemplateData
): Promise<MetadataTemplate | null> {
  if (!clientUserIdForPath || !templateId) {
    console.error('[SvcUpdateTemplate] Invalid userId or templateId provided.');
    return null;
  }
  if (!auth || !auth.currentUser || auth.currentUser.uid !== clientUserIdForPath) {
    console.error('[SvcUpdateTemplate] Auth error or userId mismatch.');
    return null;
  }

  const templateRef = doc(firestore, `users/${clientUserIdForPath}/templates`, templateId);
  const dataToUpdate: any = { ...templateData, updatedAt: serverTimestamp() };

  // Remove undefined fields from templateData to avoid overwriting with undefined
  Object.keys(dataToUpdate).forEach(key => {
    if (dataToUpdate[key] === undefined) {
      delete dataToUpdate[key];
    } else if (typeof dataToUpdate[key] === 'string') {
      dataToUpdate[key] = dataToUpdate[key].trim();
    }
  });


  try {
    await updateDoc(templateRef, dataToUpdate);
    const updatedDocSnap = await getDoc(templateRef);
    if (updatedDocSnap.exists()) {
      const data = updatedDocSnap.data();
      return {
        id: updatedDocSnap.id,
        userId: data.userId,
        name: data.name,
        ...data, // spread the rest of the fields
      } as MetadataTemplate;
    }
    return null;
  } catch (error) {
    console.error(`[SvcUpdateTemplate] Firestore 'updateDoc' FAILED for template ${templateId}:`, error);
    return null;
  }
}

export async function deleteTemplate(
  clientUserIdForPath: string,
  templateId: string
): Promise<boolean> {
  if (!clientUserIdForPath || !templateId) {
    console.error('[SvcDeleteTemplate] Invalid userId or templateId provided.');
    return false;
  }
  if (!auth || !auth.currentUser || auth.currentUser.uid !== clientUserIdForPath) {
    console.error('[SvcDeleteTemplate] Auth error or userId mismatch.');
    return false;
  }

  const templateRef = doc(firestore, `users/${clientUserIdForPath}/templates`, templateId);
  try {
    await deleteDoc(templateRef);
    return true;
  } catch (error) {
    console.error(`[SvcDeleteTemplate] Firestore 'deleteDoc' FAILED for template ${templateId}:`, error);
    return false;
  }
}
