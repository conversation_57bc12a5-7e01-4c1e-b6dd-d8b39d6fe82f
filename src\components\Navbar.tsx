
'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { LogOutIcon, LogInIcon, UserCircleIcon, GemIcon, Loader2, SettingsIcon, LayoutGridIcon } from 'lucide-react';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { Badge } from '@/components/ui/badge';
import { PLANS } from '@/config/plans';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function Navbar() {
  const { user, logout, loading: authLoading } = useAuth();
  const { subscription, loadingSubscription } = useSubscription();

  const isLoading = authLoading || loadingSubscription;

  return (
    <nav className="bg-background/80 backdrop-blur-sm shadow-sm sticky top-0 z-50">
      <div className="max-w-6xl mx-auto px-4 py-3 flex justify-between items-center">
        <Link href="/" className="flex items-center gap-2">
          <div className="inline-flex items-center justify-center bg-secondary rounded-full p-2 shadow-md overflow-hidden">
 <Image
 src="/ms-icon-150x150.png"
 alt="MetaPDF Logo"
 width={40}
 height={40}
            className="relative h-full w-full object-cover rounded-full"
 priority
 data-ai-hint="app icon"
 />
 </div>
          <span className="sr-only">MetaPDF</span> {/* Screen reader only if logo contains text */}
        </Link>
        <div className="flex items-center space-x-4">
            <Link href="/pricing" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
              Pricing
            </Link>
          {isLoading ? (
             <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
          ) : user ? (
            <div className="flex items-center gap-3">
              {subscription && subscription.planId !== 'free' && (
                <Badge variant="outline" className="border-accent text-accent font-semibold flex items-center gap-1.5 px-2.5 py-1 text-xs">
                  <GemIcon className="h-3.5 w-3.5" /> {PLANS.find(p => p.id === subscription.planId)?.name || 'Pro'}
                </Badge>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild className="cursor-pointer">
                  <Button variant="outline" className="flex items-center gap-1.5 px-3 py-2 h-auto text-sm focus-visible:ring-0 focus-visible:ring-offset-0 rounded-full">
                    <UserCircleIcon className="h-5 w-5 text-muted-foreground" />
                    <span className="truncate max-w-[100px] sm:max-w-[150px]">{user.email}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile">
                      <SettingsIcon className="mr-2 h-4 w-4" />
                      <span>Profile & Settings</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                     <Link href="/">
                      <LayoutGridIcon className="mr-2 h-4 w-4" />
                      <span>PDF Editor</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout}>
                    <LogOutIcon className="mr-2 h-4 w-4" />
                    <span>Logout</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ) : (
            <Button variant="default" size="sm" asChild>
              <Link href="/login">
                <LogInIcon className="mr-2 h-4 w-4" />
                Login / Sign Up
              </Link>
            </Button>
          )}
        </div>
      </div>
    </nav>
  );
}
