
"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { Metadata } from '@/types';

interface BulkEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyBulkEdit: (field: keyof Metadata, value: string) => void;
  numFilesSelected: number;
}

const metadataFields: { value: keyof Metadata; label: string }[] = [
  { value: "title", label: "Title" },
  { value: "author", label: "Author" },
  { value: "subject", label: "Subject" },
  { value: "keywords", label: "Keywords" },
  { value: "producer", label: "Producer" },
  { value: "creatorTool", label: "Creator Tool" },
];

const BulkEditDialog: React.FC<BulkEditDialogProps> = ({ isOpen, onClose, onApplyBulkEdit, numFilesSelected }) => {
  const [selectedField, setSelectedField] = useState<keyof Metadata>('title');
  const [value, setValue] = useState('');

  const handleSubmit = () => {
    if (selectedField && value.trim() !== "") {
      onApplyBulkEdit(selectedField, value);
      onClose();
      setValue(''); 
    }
  };

  if (numFilesSelected === 0 && isOpen) {
     onClose();
     return null;
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] bg-card text-card-foreground">
        <DialogHeader>
          <DialogTitle>Bulk Edit Metadata</DialogTitle>
          <DialogDescription>
            Apply changes to the selected {numFilesSelected} file(s). This will overwrite existing values for the chosen field.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="metadata-field" className="text-right">
              Field
            </Label>
            <Select
              value={selectedField}
              onValueChange={(val) => setSelectedField(val as keyof Metadata)}
            >
              <SelectTrigger id="metadata-field" className="col-span-3">
                <SelectValue placeholder="Select a field" />
              </SelectTrigger>
              <SelectContent>
                {metadataFields.map(field => (
                  <SelectItem key={field.value} value={field.value}>
                    {field.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="metadata-value" className="text-right">
              Value
            </Label>
            <Input
              id="metadata-value"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              className="col-span-3"
              placeholder={`Enter new ${selectedField}`}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button type="submit" onClick={handleSubmit} disabled={!selectedField || value.trim() === ""}>Apply to Selected</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BulkEditDialog;
