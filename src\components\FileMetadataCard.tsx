
"use client";

import React from 'react';
import type { PdfFile, Metadata } from '@/types';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { LightbulbIcon, Loader2Icon, SparklesIcon, ChevronDown } from 'lucide-react'; // Added ChevronDown
import { Badge } from '@/components/ui/badge';
import SeoOptimizedInput from './SeoOptimizedInput';
import { Label } from '@/components/ui/label';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FileMetadataCardProps {
  file: PdfFile;
  onUpdateFile: (fileId: string, updatedMetadata: Partial<Metadata>, updatedFields?: Partial<Pick<PdfFile, 'isLoadingKeywords' | 'suggestedKeywords' | 'isLoadingSubject'>>) => void;
  isSeoView: boolean;
  handleSuggestKeywords: (file: PdfFile) => Promise<void>;
  handleSuggestSubject: (file: PdfFile) => Promise<void>;
  seoTooltips: { title: string; subject: string; keywords: string; };
}

const FileMetadataCard: React.FC<FileMetadataCardProps> = ({
  file,
  onUpdateFile,
  isSeoView,
  handleSuggestKeywords,
  handleSuggestSubject,
  seoTooltips,
}) => {
  const handleInputChange = (field: keyof Metadata, value: string) => {
    onUpdateFile(file.id, { [field]: value });
  };

  const defaultOpenAccordionItems = React.useMemo(() => {
    const metadataFieldKeys = ["title", "author", "subject", "keywords", "producer", "creatorTool"];
    const items = [...metadataFieldKeys];
    if (isSeoView) {
      items.push('seo-keyword-suggestions');
    }
    return items;
  }, [isSeoView]);

  return (
    <div className="pt-2"> {/* Was pt-6, reduced since outer accordion has padding */}
        <Accordion type="multiple" defaultValue={defaultOpenAccordionItems} className="w-full space-y-2">
          {/* Title Field */}
          <AccordionItem value="title">
            <AccordionTrigger className="hover:no-underline py-3">
              <Label htmlFor={`title-${file.id}`} className="text-sm font-medium cursor-pointer">Title</Label>
            </AccordionTrigger>
            <AccordionContent className="pt-2 pb-4 space-y-1.5">
              {isSeoView ? (
                <SeoOptimizedInput
                  id={`title-${file.id}`}
                  value={file.metadata.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  maxLength={60}
                  tooltipContent={seoTooltips.title}
                  placeholder="Enter title"
                />
              ) : (
                <Input
                  id={`title-${file.id}`}
                  value={file.metadata.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter title"
                  aria-label={`Title for ${file.name}`}
                />
              )}
            </AccordionContent>
          </AccordionItem>

          {/* Author Field */}
          <AccordionItem value="author">
            <AccordionTrigger className="hover:no-underline py-3">
              <Label htmlFor={`author-${file.id}`} className="text-sm font-medium cursor-pointer">Author</Label>
            </AccordionTrigger>
            <AccordionContent className="pt-2 pb-4 space-y-1.5">
              <Input
                id={`author-${file.id}`}
                value={file.metadata.author}
                onChange={(e) => handleInputChange('author', e.target.value)}
                placeholder="Enter author"
                aria-label={`Author for ${file.name}`}
              />
            </AccordionContent>
          </AccordionItem>

          {/* Subject Field */}
          <AccordionItem value="subject">
            <AccordionTrigger asChild className="py-3">
              <div className="flex flex-1 items-center justify-between w-full cursor-pointer hover:no-underline font-medium">
                <Label htmlFor={`subject-${file.id}`} className="text-sm font-medium">Subject / Description</Label>
                <div className="flex items-center">
                  {isSeoView && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => { e.stopPropagation(); handleSuggestSubject(file); }}
                      disabled={file.isLoadingSubject}
                      className="text-xs px-2 py-1 h-auto text-primary hover:text-primary/80 mr-2"
                    >
                      {file.isLoadingSubject ? (
                        <Loader2Icon className="mr-1 h-3 w-3 animate-spin" />
                      ) : (
                        <SparklesIcon className="mr-1 h-3 w-3" />
                      )}
                      Suggest
                    </Button>
                  )}
                  <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200 text-muted-foreground group-data-[state=open]:rotate-180" />
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="pt-2 pb-4 space-y-1.5">
              {isSeoView ? (
                <SeoOptimizedInput
                  id={`subject-${file.id}`}
                  isTextarea
                  value={file.metadata.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  maxLength={160}
                  tooltipContent={seoTooltips.subject}
                  placeholder="Enter subject/description"
                  className="min-h-[80px]"
                />
              ) : (
                <Textarea
                  id={`subject-${file.id}`}
                  value={file.metadata.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  placeholder="Enter subject/description"
                  className="min-h-[80px]"
                  aria-label={`Subject for ${file.name}`}
                />
              )}
            </AccordionContent>
          </AccordionItem>

          {/* Keywords Field */}
          <AccordionItem value="keywords">
            <AccordionTrigger className="hover:no-underline py-3">
              <Label htmlFor={`keywords-${file.id}`} className="text-sm font-medium cursor-pointer">Keywords (comma-separated)</Label>
            </AccordionTrigger>
            <AccordionContent className="pt-2 pb-4 space-y-1.5">
              {isSeoView ? (
                <SeoOptimizedInput
                  id={`keywords-${file.id}`}
                  isTextarea
                  value={file.metadata.keywords}
                  onChange={(e) => handleInputChange('keywords', e.target.value)}
                  tooltipContent={seoTooltips.keywords}
                  placeholder="keyword1, keyword2, ..."
                  className="min-h-[80px]"
                />
              ) : (
                <Textarea
                  id={`keywords-${file.id}`}
                  value={file.metadata.keywords}
                  onChange={(e) => handleInputChange('keywords', e.target.value)}
                  placeholder="keyword1, keyword2, ..."
                  className="min-h-[80px]"
                  aria-label={`Keywords for ${file.name}`}
                />
              )}
            </AccordionContent>
          </AccordionItem>

          {/* Producer Field */}
          <AccordionItem value="producer">
            <AccordionTrigger className="hover:no-underline py-3">
              <Label htmlFor={`producer-${file.id}`} className="text-sm font-medium cursor-pointer">Producer</Label>
            </AccordionTrigger>
            <AccordionContent className="pt-2 pb-4 space-y-1.5">
              <Input
                id={`producer-${file.id}`}
                value={file.metadata.producer}
                onChange={(e) => handleInputChange('producer', e.target.value)}
                placeholder="Enter producer"
                aria-label={`Producer for ${file.name}`}
              />
            </AccordionContent>
          </AccordionItem>

          {/* Creator Tool Field */}
          <AccordionItem value="creatorTool">
            <AccordionTrigger className="hover:no-underline py-3">
              <Label htmlFor={`creatorTool-${file.id}`} className="text-sm font-medium cursor-pointer">Creator Tool</Label>
            </AccordionTrigger>
            <AccordionContent className="pt-2 pb-4 space-y-1.5">
              <Input
                id={`creatorTool-${file.id}`}
                value={file.metadata.creatorTool}
                onChange={(e) => handleInputChange('creatorTool', e.target.value)}
                placeholder="Enter creator tool"
                aria-label={`Creator tool for ${file.name}`}
              />
            </AccordionContent>
          </AccordionItem>

          {/* SEO View - Keyword Suggestions */}
          {isSeoView && (
            <AccordionItem value="seo-keyword-suggestions">
              <AccordionTrigger className="hover:no-underline py-3">
                <h4 className="text-sm font-medium text-muted-foreground cursor-pointer">Keyword Optimization</h4>
              </AccordionTrigger>
              <AccordionContent className="pt-2 pb-4 space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSuggestKeywords(file)}
                  disabled={file.isLoadingKeywords}
                  className="w-full justify-start text-left mb-2"
                >
                  {file.isLoadingKeywords ? (
                    <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <LightbulbIcon className="mr-2 h-4 w-4" />
                  )}
                  Suggest Keywords
                </Button>
                {file.suggestedKeywords && file.suggestedKeywords.length > 0 && (
                  <div className="flex flex-wrap gap-1.5 mt-1">
                    {file.suggestedKeywords.map((kw, idx) => (
                      <Badge
                        key={idx}
                        variant="secondary"
                        className="cursor-pointer hover:bg-primary/20 transition-colors"
                        onClick={() => {
                          const currentKeywords = file.metadata.keywords.split(',').map(k => k.trim()).filter(Boolean);
                          if (!currentKeywords.includes(kw)) {
                            onUpdateFile(file.id, { keywords: [...currentKeywords, kw].join(', ') });
                          }
                        }}
                      >
                        {kw}
                      </Badge>
                    ))}
                  </div>
                )}
                {file.isLoadingKeywords === false && (!file.suggestedKeywords || file.suggestedKeywords.length === 0) && (
                  <p className="text-xs text-muted-foreground">No keyword suggestions available or generated yet. Click above to try.</p>
                )}
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>
    </div>
  );
};

export default React.memo(FileMetadataCard);
