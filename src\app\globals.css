
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 200 20% 95%; /* Light gray background */
    --foreground: 180 10% 20%; /* Dark gray text */
    --card: 0 0% 100%; /* White */
    --card-foreground: 180 10% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 180 10% 15%;
    --primary: 180 60% 30%; /* Deep teal */
    --primary-foreground: 0 0% 100%; /* White */
    --primary-hover: 180 60% 25%; /* Darker primary on hover */
    --secondary: 170 70% 55%; /* Vibrant teal */
    --secondary-foreground: 180 10% 10%; /* Dark text on secondary */
    --muted: 200 15% 85%; /* Muted gray */
    --muted-foreground: 180 10% 40%; /* Muted text color */
    --accent: 20 80% 60%; /* Red-orange accent */
    --accent-foreground: 0 0% 100%; /* White text on accent */
    --accent-hover: 20 80% 55%; /* Darker accent on hover */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 200 15% 75%; /* Light gray border */
    --input: 0 0% 100%; /* White input background */
    --ring: 180 60% 40%; /* Ring color based on primary */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 180 20% 15%; /* Darker teal for sidebar in light mode */
    --sidebar-foreground: 180 10% 90%;
    --sidebar-primary: 180 60% 40%;
    --sidebar-primary-foreground: 180 10% 5%;
    --sidebar-accent: 180 20% 20%;
    --sidebar-accent-foreground: 180 10% 90%;
    --sidebar-border: 180 15% 18%;
    --sidebar-ring: 180 60% 50%;
  }

  .dark {
    --background: 180 20% 10%; /* Very dark teal background */
    --foreground: 180 10% 90%; /* Light gray text */
    --card: 180 20% 15%; /* Dark teal cards */
    --card-foreground: 180 10% 95%;
    --popover: 180 20% 15%;
    --popover-foreground: 180 10% 95%;
    --primary: 180 60% 40%; /* Lighter deep teal */
    --primary-foreground: 180 10% 5%; /* Very dark text on primary */
    --primary-hover: 180 60% 45%; /* Lighter primary on hover */
    --secondary: 170 70% 65%; /* Lighter vibrant teal */
    --secondary-foreground: 180 10% 5%; /* Very dark text on secondary */
    --muted: 180 15% 25%; /* Muted dark teal */
    --muted-foreground: 180 10% 70%; /* Muted text color */
    --accent: 20 80% 70%; /* Lighter red-orange accent */
    --accent-foreground: 180 10% 5%; /* Very dark text on accent */
    --accent-hover: 20 80% 75%; /* Lighter accent on hover */
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 98%;
    --border: 180 15% 20%; /* Dark teal border */
    --input: 180 20% 18%; /* Dark teal input */
    --ring: 180 60% 50%; /* Ring color based on primary */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 180 20% 8%; /* Very dark teal for sidebar in dark mode */
    --sidebar-foreground: 180 10% 95%;
    --sidebar-primary: 180 60% 50%;
    --sidebar-primary-foreground: 180 10% 3%;
    --sidebar-accent: 180 20% 20%;
    --sidebar-accent-foreground: 180 10% 95%;
    --sidebar-border: 180 15% 18%;
    --sidebar-ring: 180 60% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-outfit antialiased;
  }
}
