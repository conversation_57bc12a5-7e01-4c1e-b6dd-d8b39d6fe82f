
'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import dynamic from 'next/dynamic'; // Import dynamic

// Dynamically import MetaPDFContent
const MetaPDFContent = dynamic(() => import('@/components/MetaPDFContent'), {
  loading: () => (
    <div className="flex flex-col justify-center items-center min-h-screen bg-background text-foreground">
      <Loader2 className="h-16 w-16 animate-spin text-primary" />
      <p className="mt-4 text-lg">Loading editor...</p>
    </div>
  ),
  ssr: false, // Typically, main interactive content like this is client-side only
});

export default function HomePage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.replace('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-background text-foreground">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
        <p className="mt-4 text-lg">Loading application...</p>
      </div>
    );
  }

  if (!user) {
    // User is not logged in and not loading, show a loader while redirecting to /login
    // This handles the brief moment before the redirect effect runs.
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-background text-foreground">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
        <p className="mt-4 text-lg">Redirecting to login...</p>
      </div>
    );
  }

  // If user is authenticated, render the main app content
  return <MetaPDFContent />;
}
