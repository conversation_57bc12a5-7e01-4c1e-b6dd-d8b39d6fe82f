
'use server';
/**
 * @fileOverview A flow that suggests a subject/description for a document based on its content.
 *
 * - suggestSubject - A function that takes document content and returns a subject suggestion.
 * - SuggestSubjectInput - The input type for the suggestSubject function.
 * - SuggestSubjectOutput - The return type for the suggestSubject function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SuggestSubjectInputSchema = z.object({
  documentContent: z
    .string()
    .describe('The content of the PDF document to analyze for a subject suggestion.'),
});
export type SuggestSubjectInput = z.infer<typeof SuggestSubjectInputSchema>;

const SuggestSubjectOutputSchema = z.object({
  suggestedSubject: z
    .string()
    .describe('A concise and SEO-friendly subject/description for the document.'),
});
export type SuggestSubjectOutput = z.infer<typeof SuggestSubjectOutputSchema>;

export async function suggestSubject(input: SuggestSubjectInput): Promise<SuggestSubjectOutput> {
  return suggestSubjectFlow(input);
}

const suggestSubjectPrompt = ai.definePrompt({
  name: 'suggestSubjectPrompt',
  input: {schema: SuggestSubjectInputSchema},
  output: {schema: SuggestSubjectOutputSchema},
  prompt: `You are an expert SEO copywriter. Analyze the following document content and generate a concise and compelling subject/description.
This subject/description should be:
- Highly relevant to the core topics and themes of the document.
- Optimized for search engine visibility (e.g., clear, keyword-rich yet natural).
- Suitable for the 'Subject' metadata field of a PDF.
- Ideally under 160 characters.
- Engaging and accurately reflect the document's purpose or main content.

Return your answer as a JSON object with a single key "suggestedSubject" whose value is the concise and compelling subject/description string.

Document Content: {{{documentContent}}}`,
});

const suggestSubjectFlow = ai.defineFlow(
  {
    name: 'suggestSubjectFlow',
    inputSchema: SuggestSubjectInputSchema,
    outputSchema: SuggestSubjectOutputSchema,
  },
  async input => {
    const {output} = await suggestSubjectPrompt(input);
    if (!output) {
      throw new Error('AI failed to generate a subject suggestion or the output was invalid.');
    }
    return output;
  }
);

